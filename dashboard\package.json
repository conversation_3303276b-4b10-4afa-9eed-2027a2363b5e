{"name": "dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.1.0", "bcryptjs": "^3.0.2", "jose": "^6.0.11", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "prisma": "^6.8.2", "tailwindcss": "^4", "typescript": "^5"}}