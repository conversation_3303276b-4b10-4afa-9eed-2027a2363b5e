# Development Setup Guide: Prescription Dashboard

## Prerequisites
- Node.js v18.18.0 or higher
- npm v9.0.0 or higher
- MySQL database server
- Git

## Environment Setup

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/dashboard.git
cd dashboard
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Configure Environment Variables
Create a `.env` file in the project root with the following variables:

```
# Database
DATABASE_URL="mysql://username:password@localhost:3306/nplabs2"

# Authentication
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# Development
NODE_ENV="development"
```

### 4. Database Setup
The project uses Prisma ORM to connect to an existing MySQL database.

```bash
# Generate Prisma client based on schema
npx prisma generate

# If you need to pull the latest schema from the database
npx prisma db pull
```

### 5. Start Development Server
```bash
npm run dev
```

The application will be available at http://localhost:3000.

## Development Workflow

### Code Structure
- `/app`: Next.js App Router components and routes
- `/app/api`: API routes
- `/app/components`: Reusable UI components
- `/prisma`: Database schema and migrations
- `/lib`: Utility functions and shared code

### Common Tasks

#### Adding a New API Endpoint
1. Create a new file in `/app/api/[endpoint]/route.ts`
2. Implement the handler functions (GET, POST, etc.)
3. Use the Prisma client for database operations

#### Creating a New Component
1. Add the component file in `/app/components/`
2. Import and use the component in your page files

#### Database Operations
Use the Prisma client for all database operations:

```typescript
import { db } from '@/lib/db';

// Example: Fetch prescriptions
const prescriptions = await db.prescriptions.findMany({
  where: { user_id: userId }
});
```

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific test file
npm test -- path/to/test-file.test.ts
```

### Test Structure
- `/app/__tests__`: Component and page tests
- `/app/api/__tests__`: API route tests

## Deployment

### Vercel Deployment
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy from main branch

### Environment Variables for Production
Ensure these are set in your Vercel project:
- `DATABASE_URL`
- `NEXTAUTH_SECRET`
- `NEXTAUTH_URL`
- `NODE_ENV="production"`

## Troubleshooting

### Common Issues

#### Prisma Client Generation Errors
```bash
# Regenerate Prisma client
npx prisma generate
```

#### Database Connection Issues
- Verify DATABASE_URL is correct
- Ensure database server is running
- Check network access to database

#### Authentication Problems
- Verify NEXTAUTH_SECRET is set
- Check user credentials in database