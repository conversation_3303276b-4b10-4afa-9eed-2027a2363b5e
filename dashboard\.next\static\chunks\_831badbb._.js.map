{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatRelativeTime(date: string | Date) {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Just now'\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`\n  \n  return formatDate(date)\n}\n\nexport function getStatusColor(status: string) {\n  const colors = {\n    submitted: 'bg-blue-100 text-blue-800 border-blue-200',\n    review: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    approved: 'bg-green-100 text-green-800 border-green-200',\n    compounding: 'bg-purple-100 text-purple-800 border-purple-200',\n    quality_check: 'bg-orange-100 text-orange-800 border-orange-200',\n    packaging: 'bg-indigo-100 text-indigo-800 border-indigo-200',\n    shipped: 'bg-cyan-100 text-cyan-800 border-cyan-200',\n    delivered: 'bg-emerald-100 text-emerald-800 border-emerald-200'\n  }\n  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'\n}\n\nexport function getStatusProgress(status: string) {\n  const statusOrder = [\n    'submitted', 'review', 'approved', 'compounding', \n    'quality_check', 'packaging', 'shipped', 'delivered'\n  ]\n  const index = statusOrder.indexOf(status)\n  return index >= 0 ? ((index + 1) / statusOrder.length) * 100 : 0\n}\n\nexport function generateOrderId() {\n  return `RX-${Date.now().toString(36).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;IACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IAC5E,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IAE/E,OAAO,WAAW;AACpB;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAS;QACb,WAAW;QACX,QAAQ;QACR,UAAU;QACV,aAAa;QACb,eAAe;QACf,WAAW;QACX,SAAS;QACT,WAAW;IACb;IACA,OAAO,MAAM,CAAC,OAA8B,IAAI;AAClD;AAEO,SAAS,kBAAkB,MAAc;IAC9C,MAAM,cAAc;QAClB;QAAa;QAAU;QAAY;QACnC;QAAiB;QAAa;QAAW;KAC1C;IACD,MAAM,QAAQ,YAAY,OAAO,CAAC;IAClC,OAAO,SAAS,IAAI,AAAC,CAAC,QAAQ,CAAC,IAAI,YAAY,MAAM,GAAI,MAAM;AACjE;AAEO,SAAS;IACd,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW,IAAI;AAC/G", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/ui/Card.tsx"], "sourcesContent": ["\"use client\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  glass?: boolean;\n}\n\nexport function Card({ children, className, hover = true, glass = false }: CardProps) {\n  const baseClasses = \"rounded-xl border transition-all duration-300\";\n  const glassClasses = glass \n    ? \"bg-white/10 backdrop-blur-lg border-white/20 shadow-xl\" \n    : \"bg-white border-gray-200 shadow-sm\";\n  const hoverClasses = hover ? \"hover:shadow-lg hover:-translate-y-1\" : \"\";\n\n  return (\n    <motion.div\n      className={cn(baseClasses, glassClasses, hoverClasses, className)}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      {children}\n    </motion.div>\n  );\n}\n\nexport function CardHeader({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn(\"p-6 pb-4\", className)}>\n      {children}\n    </div>\n  );\n}\n\nexport function CardContent({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <div className={cn(\"p-6 pt-0\", className)}>\n      {children}\n    </div>\n  );\n}\n\nexport function CardTitle({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <h3 className={cn(\"text-lg font-semibold text-gray-900\", className)}>\n      {children}\n    </h3>\n  );\n}\n\nexport function CardDescription({ children, className }: { children: React.ReactNode; className?: string }) {\n  return (\n    <p className={cn(\"text-sm text-gray-600 mt-1\", className)}>\n      {children}\n    </p>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AAFA;;;;AAWO,SAAS,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ,KAAK,EAAa;IAClF,MAAM,cAAc;IACpB,MAAM,eAAe,QACjB,2DACA;IACJ,MAAM,eAAe,QAAQ,yCAAyC;IAEtE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc,cAAc;QACvD,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE3B;;;;;;AAGP;KAjBgB;AAmBT,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACnG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAC5B;;;;;;AAGP;MANgB;AAQT,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACpG,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAC5B;;;;;;AAGP;MANgB;AAQT,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAqD;IAClG,qBACE,6LAAC;QAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBACtD;;;;;;AAGP;MANgB;AAQT,SAAS,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAqD;IACxG,qBACE,6LAAC;QAAE,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC5C;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/ui/Button.tsx"], "sourcesContent": ["\"use client\";\nimport { forwardRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"primary\", size = \"md\", loading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl\",\n      secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500\",\n      outline: \"border-2 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500\",\n      ghost: \"text-gray-600 hover:bg-gray-100 focus:ring-gray-500\",\n      danger: \"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl\"\n    };\n    \n    const sizes = {\n      sm: \"px-3 py-1.5 text-sm\",\n      md: \"px-4 py-2 text-sm\",\n      lg: \"px-6 py-3 text-base\"\n    };\n\n    return (\n      <motion.button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        disabled={disabled || loading}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </motion.button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAYA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACvB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/UploadForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useState, FormEvent, useCallback } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useDropzone } from \"react-dropzone\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport toast from \"react-hot-toast\";\nimport {\n  CloudArrowUpIcon,\n  PhotoIcon,\n  XMarkIcon,\n  DocumentIcon,\n  CheckCircleIcon\n} from \"@heroicons/react/24/outline\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"./ui/Card\";\nimport Button from \"./ui/Button\";\nimport { cn } from \"@/lib/utils\";\n\ninterface UploadFormProps {\n  onSuccess?: () => void;\n}\n\nexport default function UploadForm({ onSuccess }: UploadFormProps) {\n  const router = useRouter();\n  const [imageUrl, setImageUrl] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setUploadedFiles([file]);\n      const url = URL.createObjectURL(file);\n      setPreviewUrl(url);\n      // For demo purposes, we'll use the object URL as the image URL\n      setImageUrl(url);\n    }\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']\n    },\n    maxFiles: 1,\n    multiple: false\n  });\n\n  const removeFile = () => {\n    setUploadedFiles([]);\n    setPreviewUrl(null);\n    setImageUrl(\"\");\n  };\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n    if (!imageUrl && uploadedFiles.length === 0) {\n      toast.error(\"Please provide an image URL or upload a file\");\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const res = await fetch(\"/api/prescriptions\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({\n          prescriptionImg: imageUrl || previewUrl,\n          description\n        }),\n      });\n      if (!res.ok) throw new Error(\"Upload failed\");\n\n      toast.success(\"Prescription uploaded successfully!\");\n      setImageUrl(\"\");\n      setDescription(\"\");\n      setUploadedFiles([]);\n      setPreviewUrl(null);\n      onSuccess?.();\n      router.refresh();\n    } catch (err) {\n      toast.error((err as Error).message || \"Error uploading prescription\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <CloudArrowUpIcon className=\"w-5 h-5 text-blue-600\" />\n          <span>Upload New Prescription</span>\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Drag & Drop Area */}\n          <div className=\"space-y-4\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Prescription Image\n            </label>\n\n            <motion.div\n              {...getRootProps()}\n              className={cn(\n                \"border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-all duration-300\",\n                isDragActive\n                  ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\"\n                  : \"border-gray-300 dark:border-gray-600 hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700/50\"\n              )}\n              whileHover={{ scale: 1.01 }}\n              whileTap={{ scale: 0.99 }}\n            >\n              <input {...getInputProps()} />\n\n              <AnimatePresence mode=\"wait\">\n                {uploadedFiles.length > 0 ? (\n                  <motion.div\n                    key=\"uploaded\"\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    exit={{ opacity: 0, scale: 0.8 }}\n                    className=\"space-y-4\"\n                  >\n                    {previewUrl && (\n                      <div className=\"relative inline-block\">\n                        <img\n                          src={previewUrl}\n                          alt=\"Preview\"\n                          className=\"w-32 h-32 object-cover rounded-lg mx-auto\"\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            removeFile();\n                          }}\n                          className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\"\n                        >\n                          <XMarkIcon className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    )}\n                    <div className=\"flex items-center justify-center space-x-2 text-green-600\">\n                      <CheckCircleIcon className=\"w-5 h-5\" />\n                      <span className=\"text-sm font-medium\">File uploaded successfully</span>\n                    </div>\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"empty\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"space-y-4\"\n                  >\n                    <PhotoIcon className=\"w-12 h-12 text-gray-400 mx-auto\" />\n                    <div>\n                      <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                        {isDragActive ? \"Drop your prescription here\" : \"Upload prescription image\"}\n                      </p>\n                      <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n                        Drag and drop or click to browse\n                      </p>\n                      <p className=\"text-xs text-gray-400 dark:text-gray-500 mt-2\">\n                        Supports: JPG, PNG, GIF, WebP (Max 10MB)\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.div>\n\n            {/* Alternative URL input */}\n            <div className=\"text-center text-sm text-gray-500 dark:text-gray-400\">\n              or\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Image URL\n              </label>\n              <input\n                type=\"url\"\n                value={imageUrl}\n                onChange={(e) => setImageUrl(e.target.value)}\n                placeholder=\"https://example.com/prescription.jpg\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n              />\n            </div>\n          </div>\n\n          {/* Description */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Description\n            </label>\n            <textarea\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"Enter prescription details (optional)\"\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none\"\n            />\n          </div>\n\n          {/* Submit Button */}\n          <Button\n            type=\"submit\"\n            disabled={loading || (!imageUrl && uploadedFiles.length === 0)}\n            loading={loading}\n            className=\"w-full\"\n            size=\"lg\"\n          >\n            {loading ? \"Uploading...\" : \"Upload Prescription\"}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;;;AAfA;;;;;;;;;;AAqBe,SAAS,WAAW,EAAE,SAAS,EAAmB;;IAC/D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC;YAC1B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,IAAI,MAAM;gBACR,iBAAiB;oBAAC;iBAAK;gBACvB,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,cAAc;gBACd,+DAA+D;gBAC/D,YAAY;YACd;QACF;yCAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;aAAQ;QACvD;QACA,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,iBAAiB,EAAE;QACnB,cAAc;QACd,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,YAAY,cAAc,MAAM,KAAK,GAAG;YAC3C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,sBAAsB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,iBAAiB,YAAY;oBAC7B;gBACF;YACF;YACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAE7B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,YAAY;YACZ,eAAe;YACf,iBAAiB,EAAE;YACnB,cAAc;YACd;YACA,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,AAAC,IAAc,OAAO,IAAI;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,kOAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;sCAC5B,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAIV,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA6D;;;;;;8CAI9E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACR,GAAG,cAAc;oCAClB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gGACA,eACI,mDACA;oCAEN,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC;4CAAO,GAAG,eAAe;;;;;;sDAE1B,6LAAC,4LAAA,CAAA,kBAAe;4CAAC,MAAK;sDACnB,cAAc,MAAM,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,MAAM;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAC/B,WAAU;;oDAET,4BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,KAAK;gEACL,KAAI;gEACJ,WAAU;;;;;;0EAEZ,6LAAC;gEACC,MAAK;gEACL,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB;gEACF;gEACA,WAAU;0EAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAI3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gOAAA,CAAA,kBAAe;gEAAC,WAAU;;;;;;0EAC3B,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;+CA3BpC;;;;qEA+BN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,MAAM;oDAAE,SAAS;gDAAE;gDACnB,WAAU;;kEAEV,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EACV,eAAe,gCAAgC;;;;;;0EAElD,6LAAC;gEAAE,WAAU;0EAAgD;;;;;;0EAG7D,6LAAC;gEAAE,WAAU;0EAAgD;;;;;;;;;;;;;+CAd3D;;;;;;;;;;;;;;;;8CAwBZ,6LAAC;oCAAI,WAAU;8CAAuD;;;;;;8CAItE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;sCAKd,6LAAC,qIAAA,CAAA,UAAM;4BACL,MAAK;4BACL,UAAU,WAAY,CAAC,YAAY,cAAc,MAAM,KAAK;4BAC5D,SAAS;4BACT,WAAU;4BACV,MAAK;sCAEJ,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GA1MwB;;QACP,qIAAA,CAAA,YAAS;QAkB8B,2KAAA,CAAA,cAAW;;;KAnB3C", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/DashboardLayout.tsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useAuth } from \"../providers\";\nimport { useTheme } from \"./ThemeProvider\";\nimport { useRouter, usePathname } from \"next/navigation\";\nimport {\n  HomeIcon,\n  PlusIcon,\n  ChartBarIcon,\n  CogIcon,\n  BellIcon,\n  SunIcon,\n  MoonIcon,\n  UserCircleIcon,\n  LogOutIcon,\n  MenuIcon,\n  XMarkIcon\n} from \"@heroicons/react/24/outline\";\nimport { cn } from \"@/lib/utils\";\nimport Button from \"./ui/Button\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'New Prescription', href: '/dashboard/new', icon: PlusIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [notifications] = useState(3); // Mock notification count\n\n  const handleLogout = async () => {\n    await logout();\n    router.push(\"/\");\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 transition-colors duration-300\">\n      {/* Mobile sidebar overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 lg:hidden\"\n          >\n            <div className=\"fixed inset-0 bg-black/50\" onClick={() => setSidebarOpen(false)} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.div\n        initial={false}\n        animate={{ x: sidebarOpen ? 0 : -320 }}\n        className={cn(\n          \"fixed inset-y-0 left-0 z-50 w-80 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-r border-gray-200 dark:border-gray-700 lg:translate-x-0 lg:static lg:inset-0\",\n          \"lg:w-64 xl:w-72\"\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Logo */}\n          <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">NP</span>\n              </div>\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                NPLabs Pro\n              </h1>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-6 space-y-2\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200\",\n                    isActive\n                      ? \"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg\"\n                      : \"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800\"\n                  )}\n                  whileHover={{ x: isActive ? 0 : 4 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <item.icon className=\"w-5 h-5 mr-3\" />\n                  {item.name}\n                </motion.a>\n              );\n            })}\n          </nav>\n\n          {/* User section */}\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <UserCircleIcon className=\"w-10 h-10 text-gray-400\" />\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                  {user?.name || 'User'}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                  {user?.email}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={toggleTheme}\n                className=\"flex-1\"\n              >\n                {theme === 'light' ? <MoonIcon className=\"w-4 h-4\" /> : <SunIcon className=\"w-4 h-4\" />}\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleLogout}\n                className=\"flex-1\"\n              >\n                <LogOutIcon className=\"w-4 h-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 xl:pl-72\">\n        {/* Top bar */}\n        <header className=\"bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200 dark:border-gray-700 sticky top-0 z-30\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800\"\n            >\n              <MenuIcon className=\"w-5 h-5\" />\n            </button>\n\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                className=\"relative p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <BellIcon className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                {notifications > 0 && (\n                  <span className=\"absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {notifications}\n                  </span>\n                )}\n              </motion.button>\n            </div>\n          </div>\n        </header>\n\n        {/* Page content */}\n        <main className=\"py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AApBA;;;;;;;;;AA0BA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAoB,MAAM;QAAkB,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACnE;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,gNAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,0BAA0B;IAE/D,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;wBAA4B,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;0BAM/E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBAAE,GAAG,cAAc,IAAI,CAAC;gBAAI;gBACrC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yKACA;0BAGF,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAG,WAAU;sDAA+F;;;;;;;;;;;;8CAI/G,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0FACA,WACI,sEACA;oCAEN,YAAY;wCAAE,GAAG,WAAW,IAAI;oCAAE;oCAClC,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAZL,KAAK,IAAI;;;;;4BAepB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,MAAM;;;;;;;;;;;;;;;;;;8CAIb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,UAAU,wBAAU,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;qEAAe,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAE7E,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,yLAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,yLAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,gBAAgB,mBACf,6LAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASb,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAxJwB;;QACG,oHAAA,CAAA,UAAO;QACD,sIAAA,CAAA,WAAQ;QACxB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAJN", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/PrescriptionCard.tsx"], "sourcesContent": ["\"use client\";\nimport { motion } from \"framer-motion\";\nimport { \n  ClockI<PERSON>, \n  EyeIcon, \n  CalendarIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon\n} from \"@heroicons/react/24/outline\";\nimport { Card, CardContent } from \"./ui/Card\";\nimport { cn, formatRelativeTime, getStatusColor, getStatusProgress } from \"@/lib/utils\";\n\ninterface Prescription {\n  id: string;\n  prescriptionImg: string;\n  description?: string;\n  status: string;\n  createdAt: string;\n}\n\ninterface PrescriptionCardProps {\n  prescription: Prescription;\n  onClick: () => void;\n  index: number;\n}\n\nconst statusIcons = {\n  submitted: ClockIcon,\n  review: ExclamationTriangleIcon,\n  approved: CheckCircleIcon,\n  compounding: ClockIcon,\n  quality_check: CheckCircleIcon,\n  packaging: ClockIcon,\n  shipped: ClockIcon,\n  delivered: CheckCircleIcon,\n};\n\nexport default function PrescriptionCard({ prescription, onClick, index }: PrescriptionCardProps) {\n  const StatusIcon = statusIcons[prescription.status as keyof typeof statusIcons] || ClockIcon;\n  const progress = getStatusProgress(prescription.status);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3, delay: index * 0.1 }}\n      whileHover={{ y: -4 }}\n      className=\"group\"\n    >\n      <Card \n        className=\"overflow-hidden cursor-pointer bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300\"\n        onClick={onClick}\n      >\n        <div className=\"relative\">\n          {/* Progress bar */}\n          <div className=\"absolute top-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700\">\n            <motion.div\n              className=\"h-full bg-gradient-to-r from-blue-500 to-purple-500\"\n              initial={{ width: 0 }}\n              animate={{ width: `${progress}%` }}\n              transition={{ duration: 1, delay: index * 0.1 + 0.3 }}\n            />\n          </div>\n\n          {/* Image */}\n          <div className=\"relative h-48 overflow-hidden\">\n            <img\n              src={prescription.prescriptionImg}\n              alt={prescription.description || \"Prescription\"}\n              className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n            />\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n            \n            {/* Status badge */}\n            <div className=\"absolute top-3 right-3\">\n              <div className={cn(\n                \"flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border backdrop-blur-sm\",\n                getStatusColor(prescription.status)\n              )}>\n                <StatusIcon className=\"w-3 h-3\" />\n                <span className=\"capitalize\">\n                  {prescription.status.replace('_', ' ')}\n                </span>\n              </div>\n            </div>\n\n            {/* View overlay */}\n            <motion.div\n              className=\"absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              initial={false}\n            >\n              <motion.div\n                className=\"flex items-center space-x-2 text-white font-medium\"\n                initial={{ scale: 0.8 }}\n                whileHover={{ scale: 1 }}\n              >\n                <EyeIcon className=\"w-5 h-5\" />\n                <span>View Details</span>\n              </motion.div>\n            </motion.div>\n          </div>\n\n          <CardContent className=\"p-4\">\n            {/* Description */}\n            {prescription.description && (\n              <h3 className=\"font-medium text-gray-900 dark:text-white mb-2 line-clamp-2\">\n                {prescription.description}\n              </h3>\n            )}\n\n            {/* Meta info */}\n            <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400\">\n              <div className=\"flex items-center space-x-1\">\n                <CalendarIcon className=\"w-4 h-4\" />\n                <span>{formatRelativeTime(prescription.createdAt)}</span>\n              </div>\n              \n              <motion.div\n                className=\"text-blue-600 dark:text-blue-400 font-medium\"\n                whileHover={{ scale: 1.05 }}\n              >\n                View →\n              </motion.div>\n            </div>\n          </CardContent>\n        </div>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AAVA;;;;;;AA0BA,MAAM,cAAc;IAClB,WAAW,oNAAA,CAAA,YAAS;IACpB,QAAQ,gPAAA,CAAA,0BAAuB;IAC/B,UAAU,gOAAA,CAAA,kBAAe;IACzB,aAAa,oNAAA,CAAA,YAAS;IACtB,eAAe,gOAAA,CAAA,kBAAe;IAC9B,WAAW,oNAAA,CAAA,YAAS;IACpB,SAAS,oNAAA,CAAA,YAAS;IAClB,WAAW,gOAAA,CAAA,kBAAe;AAC5B;AAEe,SAAS,iBAAiB,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAyB;IAC9F,MAAM,aAAa,WAAW,CAAC,aAAa,MAAM,CAA6B,IAAI,oNAAA,CAAA,YAAS;IAC5F,MAAM,WAAW,CAAA,GAAA,+GAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa,MAAM;IAEtD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,WAAU;kBAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;YACH,WAAU;YACV,SAAS;sBAET,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;4BAAC;4BACjC,YAAY;gCAAE,UAAU;gCAAG,OAAO,QAAQ,MAAM;4BAAI;;;;;;;;;;;kCAKxD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK,aAAa,eAAe;gCACjC,KAAK,aAAa,WAAW,IAAI;gCACjC,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,kGACA,CAAA,GAAA,+GAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,MAAM;;sDAElC,6LAAC;4CAAW,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDACb,aAAa,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;0CAMxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAI;oCACtB,YAAY;wCAAE,OAAO;oCAAE;;sDAEvB,6LAAC,gNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;4BAEpB,aAAa,WAAW,kBACvB,6LAAC;gCAAG,WAAU;0CACX,aAAa,WAAW;;;;;;0CAK7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,6LAAC;0DAAM,CAAA,GAAA,+GAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;kDAGlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;kDAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KA5FwB", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useRouter } from \"next/navigation\";\nimport UploadForm from \"@/app/components/UploadForm\";\nimport DashboardLayout from \"@/app/components/DashboardLayout\";\nimport PrescriptionCard from \"@/app/components/PrescriptionCard\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/app/components/ui/Card\";\nimport {\n  PlusIcon,\n  ChartBarIcon,\n  ClockIcon,\n  CheckCircleIcon\n} from \"@heroicons/react/24/outline\";\n\ninterface Prescription {\n  id: string;\n  prescriptionImg: string;\n  description?: string;\n  status: string;\n  createdAt: string;\n}\n\nexport default function DashboardPage() {\n  const router = useRouter();\n  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showUploadForm, setShowUploadForm] = useState(false);\n\n  const fetchPrescriptions = async () => {\n    try {\n      setLoading(true);\n      const res = await fetch(\"/api/prescriptions\");\n      if (!res.ok) throw new Error(\"Failed to fetch\");\n      const data = await res.json();\n      setPrescriptions(data);\n    } catch (error) {\n      console.error(\"Error fetching prescriptions:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchPrescriptions();\n  }, []);\n\n  // Calculate stats\n  const stats = {\n    total: prescriptions.length,\n    pending: prescriptions.filter(p => ['submitted', 'review'].includes(p.status)).length,\n    inProgress: prescriptions.filter(p => ['approved', 'compounding', 'quality_check', 'packaging'].includes(p.status)).length,\n    completed: prescriptions.filter(p => ['shipped', 'delivered'].includes(p.status)).length,\n  };\n\n  return (\n    <DashboardLayout>\n      <div className=\"px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto space-y-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\"\n        >\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              Dashboard\n            </h1>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n              Manage and track your prescription orders\n            </p>\n          </div>\n\n          <motion.button\n            onClick={() => setShowUploadForm(!showUploadForm)}\n            className=\"mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl\"\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <PlusIcon className=\"w-5 h-5 mr-2\" />\n            New Prescription\n          </motion.button>\n        </motion.div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {[\n            { label: 'Total', value: stats.total, icon: ChartBarIcon, color: 'blue' },\n            { label: 'Pending', value: stats.pending, icon: ClockIcon, color: 'yellow' },\n            { label: 'In Progress', value: stats.inProgress, icon: ClockIcon, color: 'purple' },\n            { label: 'Completed', value: stats.completed, icon: CheckCircleIcon, color: 'green' },\n          ].map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <Card className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                        {stat.label}\n                      </p>\n                      <p className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                        {stat.value}\n                      </p>\n                    </div>\n                    <div className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}>\n                      <stat.icon className={`w-6 h-6 text-${stat.color}-600 dark:text-${stat.color}-400`} />\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Upload Form */}\n        {showUploadForm && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <UploadForm onSuccess={() => {\n              fetchPrescriptions();\n              setShowUploadForm(false);\n            }} />\n          </motion.div>\n        )}\n\n        {/* Prescriptions Grid */}\n        {loading ? (\n          <div className=\"grid sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[...Array(6)].map((_, i) => (\n              <div key={i} className=\"animate-pulse\">\n                <div className=\"bg-gray-200 dark:bg-gray-700 rounded-xl h-64\"></div>\n              </div>\n            ))}\n          </div>\n        ) : prescriptions.length === 0 ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-center py-16\"\n          >\n            <div className=\"max-w-md mx-auto\">\n              <div className=\"w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <PlusIcon className=\"w-8 h-8 text-gray-400\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                No prescriptions yet\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n                Get started by uploading your first prescription\n              </p>\n              <motion.button\n                onClick={() => setShowUploadForm(true)}\n                className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <PlusIcon className=\"w-5 h-5 mr-2\" />\n                Upload Prescription\n              </motion.button>\n            </div>\n          </motion.div>\n        ) : (\n          <div className=\"grid sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {prescriptions.map((prescription, index) => (\n              <PrescriptionCard\n                key={prescription.id}\n                prescription={prescription}\n                index={index}\n                onClick={() => router.push(`/dashboard/${prescription.id}`)}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;;AAuBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,qBAAqB;QACzB,IAAI;YACF,WAAW;YACX,MAAM,MAAM,MAAM,MAAM;YACxB,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;YAC3B,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,QAAQ;QACZ,OAAO,cAAc,MAAM;QAC3B,SAAS,cAAc,MAAM,CAAC,CAAA,IAAK;gBAAC;gBAAa;aAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;QACrF,YAAY,cAAc,MAAM,CAAC,CAAA,IAAK;gBAAC;gBAAY;gBAAe;gBAAiB;aAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;QAC1H,WAAW,cAAc,MAAM,CAAC,CAAA,IAAK;gBAAC;gBAAW;aAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;IAC1F;IAEA,qBACE,6LAAC,wIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;8CAGjE,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAKvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS,IAAM,kBAAkB,CAAC;4BAClC,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAMzC,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,OAAO;4BAAS,OAAO,MAAM,KAAK;4BAAE,MAAM,0NAAA,CAAA,eAAY;4BAAE,OAAO;wBAAO;wBACxE;4BAAE,OAAO;4BAAW,OAAO,MAAM,OAAO;4BAAE,MAAM,oNAAA,CAAA,YAAS;4BAAE,OAAO;wBAAS;wBAC3E;4BAAE,OAAO;4BAAe,OAAO,MAAM,UAAU;4BAAE,MAAM,oNAAA,CAAA,YAAS;4BAAE,OAAO;wBAAS;wBAClF;4BAAE,OAAO;4BAAa,OAAO,MAAM,SAAS;4BAAE,MAAM,gOAAA,CAAA,kBAAe;4BAAE,OAAO;wBAAQ;qBACrF,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAI;sCAEjC,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;;;;;;;0DAGf,6LAAC;gDAAI,WAAW,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC;0DAClF,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAW,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,eAAe,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAjBrF,KAAK,KAAK;;;;;;;;;;gBA2BpB,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC,mIAAA,CAAA,UAAU;wBAAC,WAAW;4BACrB;4BACA,kBAAkB;wBACpB;;;;;;;;;;;gBAKH,wBACC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;;;;;2BADP;;;;;;;;;2BAKZ,cAAc,MAAM,KAAK,kBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS,IAAM,kBAAkB;gCACjC,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;yCAM3C,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,6LAAC,yIAAA,CAAA,UAAgB;4BAEf,cAAc;4BACd,OAAO;4BACP,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,aAAa,EAAE,EAAE;2BAHrD,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;AAWpC;GAlKwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}