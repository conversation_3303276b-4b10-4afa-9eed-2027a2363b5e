import { NextResponse } from "next/server";
import { db } from "../../../lib/db";
import { cookies } from "next/headers";
import { jwtVerify } from "jose";

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || "development-secret"
);

// Helper function to get user from token
async function getUserFromToken() {
  try {
    const cookieList = await cookies();
    const sessionToken = cookieList.get('auth-token')?.value;

    if (!sessionToken) {
      return null;
    }

    const { payload } = await jwtVerify(sessionToken, JWT_SECRET);
    return payload.user as { id: string; email: string; name: string };
  } catch (error) {
    return null;
  }
}

export async function GET() {
  const user = await getUserFromToken();
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const prescriptions = await db.prescription.findMany({
    where: { userId: user.id },
    orderBy: { createdAt: "desc" },
    include: {
      orders: true
    }
  });

  return NextResponse.json(prescriptions);
}

export async function POST(request: Request) {
  const user = await getUserFromToken();
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { prescriptionImg, description } = await request.json();

  if (!prescriptionImg) {
    return NextResponse.json({ error: "Prescription image is required" }, { status: 400 });
  }

  const prescription = await db.prescription.create({
    data: {
      userId: user.id,
      prescriptionImg,
      description,
      status: "submitted"
    },
  });

  return NextResponse.json(prescription);
}
