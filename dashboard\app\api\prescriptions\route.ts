import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]/route";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const userId = Number(session.user.id);
  const prescriptions = await prisma.prescriptions.findMany({
    where: { user_id: BigInt(userId) },
    orderBy: { created_at: "desc" },
  });
  return NextResponse.json(prescriptions);
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const userId = Number(session.user.id);
  const { prescription_img, description } = await request.json();
  const prescription = await prisma.prescriptions.create({
    data: {
      user_id: BigInt(userId),
      prescription_img,
      description,
    },
  });
  return NextResponse.json(prescription);
}
