import mysql.connector
from collections import Counter
from datetime import datetime

def connect_db():
    try:
        return mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="nplabs dev"
        )
    except mysql.connector.Error as err:
        print(f"Error connecting to database: {err}")
        return None

def analyze_users(conn):
    cursor = conn.cursor(dictionary=True)
    
    # Basic user stats
    cursor.execute("""
        SELECT 
            COUNT(*) as total_users,
            COUNT(DISTINCT country) as countries_represented,
            MIN(created_at) as first_signup,
            MAX(created_at) as latest_signup
        FROM users
    """)
    user_stats = cursor.fetchone()
    
    # User growth over time
    cursor.execute("""
        SELECT 
            DATE(created_at) as signup_date,
            COUNT(*) as new_users,
            SUM(COUNT(*)) OVER (ORDER BY DATE(created_at)) as total_users
        FROM users
        WHERE created_at IS NOT NULL
        GROUP BY DATE(created_at)
        ORDER BY signup_date
    """)
    growth_data = cursor.fetchall()
    
    # Top countries
    cursor.execute("""
        SELECT 
            country,
            COUNT(*) as user_count 
        FROM users 
        WHERE country IS NOT NULL AND country != ''
        GROUP BY country 
        ORDER BY user_count DESC
        LIMIT 5
    """)
    top_countries = cursor.fetchall()
    
    return {
        'stats': user_stats,
        'growth': growth_data,
        'top_countries': top_countries
    }

def analyze_tickets(conn):
    cursor = conn.cursor(dictionary=True)
    ticket_data = {'stats': None, 'common_subjects': []}
    
    try:
        # Basic ticket stats
        cursor.execute("""
            SELECT 
                COUNT(*) as total_tickets,
                COUNT(DISTINCT user_id) as users_with_tickets,
                AVG(TIMESTAMPDIFF(HOUR, created_at, COALESCE(updated_at, created_at))) as avg_resolution_hours
            FROM tickets
        """)
        ticket_data['stats'] = cursor.fetchone()
        
        # Common ticket subjects (from the first few words)
        cursor.execute("""
            SELECT 
                LOWER(SUBSTRING_INDEX(SUBJECT, ' ', 5)) as subject_start,
                COUNT(*) as count
            FROM tickets
            WHERE subject IS NOT NULL AND subject != ''
            GROUP BY subject_start
            HAVING count > 3
            ORDER BY count DESC
            LIMIT 10
        """)
        ticket_data['common_subjects'] = cursor.fetchall()
        
        # Try to get status distribution if status_id exists
        try:
            cursor.execute("""
                SELECT 
                    status_id,
                    COUNT(*) as count
                FROM tickets
                WHERE status_id IS NOT NULL
                GROUP BY status_id
                ORDER BY count DESC
            """)
            ticket_data['status_distribution'] = cursor.fetchall()
        except:
            pass
            
    except mysql.connector.Error as err:
        print(f"Warning: Could not analyze tickets - {err}")
    
    return ticket_data

def analyze_medication_mentions(conn):
    cursor = conn.cursor(dictionary=True)
    
    # Common medication mentions in tickets
    cursor.execute("""
        SELECT 
            LOWER(SUBSTRING_INDEX(SUBSTRING_INDEX(
                CONCAT(t.subject, ' ', t.content), ' ', n.n), ' ', -1)
            ) as word,
            COUNT(*) as mention_count
        FROM tickets t
        JOIN (
            SELECT 1 as n UNION SELECT 2 UNION SELECT 3 UNION 
            SELECT 4 UNION SELECT 5
        ) n
        WHERE LENGTH(LOWER(SUBSTRING_INDEX(SUBSTRING_INDEX(
            CONCAT(t.subject, ' ', t.content), ' ', n.n), ' ', -1)
        )) > 4  -- Only words with more than 4 characters
        AND LOWER(SUBSTRING_INDEX(SUBSTRING_INDEX(
            CONCAT(t.subject, ' ', t.content), ' ', n.n), ' ', -1)
        ) IN (
            'testosterone', 'viagra', 'sildenafil', 'tadalafil', 'estrogen',
            'progesterone', 'thyroid', 'hgh', 'b12', 'melatonin', 'dhea',
            'pregnenolone', 'cream', 'gel', 'capsule', 'tablet', 'injection'
        )
        GROUP BY word
        ORDER BY mention_count DESC
        LIMIT 15
    """)
    
    return cursor.fetchall()

def generate_report():
    conn = connect_db()
    if not conn:
        print("Failed to connect to database.")
        return
    
    try:
        print("\n" + "="*80)
        print("COMPOUNDING PHARMACY BUSINESS INTELLIGENCE REPORT")
        print("Generated on:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("="*80)
        
        # User Analysis
        print("\n" + "="*80)
        print("USER ANALYSIS")
        print("="*80)
        
        user_data = analyze_users(conn)
        if user_data['stats']:
            stats = user_data['stats']
            print(f"\nTotal Users: {stats['total_users']}")
            print(f"Countries Represented: {stats['countries_represented']}")
            print(f"First Signup: {stats['first_signup']}")
            print(f"Latest Signup: {stats['latest_signup']}")
            
            print("\nTop Countries by User Count:")
            for country in user_data['top_countries']:
                print(f"  - {country['country']}: {country['user_count']} users")
        
        # Ticket Analysis
        print("\n" + "="*80)
        print("TICKET ANALYSIS")
        print("="*80)
        
        ticket_data = analyze_tickets(conn)
        if ticket_data.get('stats'):
            stats = ticket_data['stats']
            print(f"\nTotal Tickets: {stats['total_tickets'] or 0}")
            print(f"Unique Users with Tickets: {stats['users_with_tickets'] or 0}")
            
            if stats['avg_resolution_hours'] is not None:
                print(f"Average Resolution Time: {float(stats['avg_resolution_hours']):.1f} hours")
            
            if ticket_data.get('status_distribution'):
                print("\nTickets by Status:")
                for status in ticket_data['status_distribution']:
                    print(f"  - Status {status['status_id']}: {status['count']} tickets")
        
        if ticket_data.get('common_subjects'):
            print("\nMost Common Ticket Subjects:")
            for subj in ticket_data['common_subjects']:
                print(f"  - {subj['subject_start']}... ({subj['count']} tickets)")
        
        # Medication Analysis
        print("\n" + "="*80)
        print("MEDICATION MENTIONS IN TICKETS")
        print("="*80)
        
        med_mentions = analyze_medication_mentions(conn)
        if med_mentions:
            print("\nMost Mentioned Medication Terms:")
            for med in med_mentions:
                print(f"  - {med['word'].title()}: {med['mention_count']} mentions")
        
        # Recommendations
        print("\n" + "="*80)
        print("KEY RECOMMENDATIONS")
        print("="*80)
        print("""
1. User Growth & Engagement:
   - Focus on top countries for localized marketing
   - Implement user retention strategies for long-term engagement

2. Support Optimization:
   - Address common ticket categories with self-service options
   - Create knowledge base articles for frequently asked questions

3. Product Focus:
   - Stock up on frequently mentioned medications
   - Consider creating educational content about popular compounds

4. Operational Improvements:
   - Analyze and improve ticket resolution times
   - Train staff on common medication inquiries
""")
        
    except Exception as e:
        print(f"\nError during analysis: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    generate_report()
