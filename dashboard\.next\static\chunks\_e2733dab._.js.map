{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/UploadForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useState, FormEvent } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport toast from \"react-hot-toast\";\n\ninterface UploadFormProps {\n}\n\nexport default function UploadForm() {\n  const router = useRouter();\n  const [imageUrl, setImageUrl] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const res = await fetch(\"/api/prescriptions\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ prescriptionImg: imageUrl, description }),\n      });\n      if (!res.ok) throw new Error(\"Upload failed\");\n      toast.success(\"Prescription uploaded\");\n      setImageUrl(\"\");\n      setDescription(\"\");\n      router.refresh();\n    } catch (err) {\n      toast.error((err as Error).message || \"Error uploading\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"mb-6 p-4 border rounded\">\n      <div className=\"mb-2\">\n        <label className=\"block text-sm font-medium\">Image URL</label>\n        <input\n          type=\"text\"\n          value={imageUrl}\n          onChange={(e) => setImageUrl(e.target.value)}\n          required\n          className=\"mt-1 w-full border rounded px-2 py-1\"\n        />\n      </div>\n      <div className=\"mb-2\">\n        <label className=\"block text-sm font-medium\">Description</label>\n        <input\n          type=\"text\"\n          value={description}\n          onChange={(e) => setDescription(e.target.value)}\n          className=\"mt-1 w-full border rounded px-2 py-1\"\n        />\n      </div>\n      <button\n        type=\"submit\"\n        disabled={loading}\n        className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\"\n      >\n        {loading ? \"Uploading...\" : \"Upload\"}\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,sBAAsB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,iBAAiB;oBAAU;gBAAY;YAChE;YACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,YAAY;YACZ,eAAe;YACf,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,AAAC,IAAc,OAAO,IAAI;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA4B;;;;;;kCAC7C,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,QAAQ;wBACR,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA4B;;;;;;kCAC7C,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,UAAU,iBAAiB;;;;;;;;;;;;AAIpC;GAzDwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/DashboardLayout.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuth } from \"../providers\";\nimport { useRouter } from \"next/navigation\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    await logout();\n    router.push(\"/\");\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation Header */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                NPLabs Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">\n                Welcome, {user?.name || user?.email}\n              </span>\n              <button\n                onClick={handleLogout}\n                className=\"text-sm text-gray-500 hover:text-gray-700\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"py-6\">\n        {children}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAItD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CAC5B,MAAM,QAAQ,MAAM;;;;;;;kDAEhC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GAzCwB;;QACG,oHAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState } from \"react\";\nimport UploadForm from \"@/app/components/UploadForm\";\nimport DashboardLayout from \"@/app/components/DashboardLayout\";\n\ninterface Prescription {\n  id: string;\n  prescriptionImg: string;\n  description?: string;\n  status: string;\n  createdAt: string;\n}\n\nexport default function DashboardPage() {\n  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);\n\n  const fetchPrescriptions = () => {\n    fetch(\"/api/prescriptions\")\n      .then((res) => {\n        if (!res.ok) throw new Error(\"Failed to fetch\");\n        return res.json();\n      })\n      .then((data) => setPrescriptions(data))\n      .catch(console.error);\n  };\n  useEffect(fetchPrescriptions, []);\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-4 max-w-7xl mx-auto\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">My Prescriptions</h2>\n          <p className=\"text-gray-600\">Upload and track your prescription status</p>\n        </div>\n\n        <UploadForm />\n\n        {prescriptions.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500\">No prescriptions yet. Upload your first prescription above!</p>\n          </div>\n        ) : (\n          <div className=\"mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {prescriptions.map((p) => (\n              <div\n                key={p.id}\n                className=\"bg-white border rounded-lg p-4 shadow hover:shadow-lg transition cursor-pointer\"\n                onClick={() => window.location.href = `/dashboard/${p.id}`}\n              >\n                <img\n                  src={p.prescriptionImg}\n                  alt={p.description || \"Prescription image\"}\n                  className=\"w-full h-48 object-cover rounded\"\n                />\n                {p.description && (\n                  <p className=\"mt-2 text-sm text-gray-700\">{p.description}</p>\n                )}\n                <div className=\"mt-2 flex justify-between items-center\">\n                  <span className={`px-2 py-1 text-xs rounded ${\n                    p.status === 'submitted' ? 'bg-blue-100 text-blue-800' :\n                    p.status === 'approved' ? 'bg-green-100 text-green-800' :\n                    p.status === 'delivered' ? 'bg-purple-100 text-purple-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {p.status.charAt(0).toUpperCase() + p.status.slice(1)}\n                  </span>\n                  <p className=\"text-xs text-gray-500\">\n                    {new Date(p.createdAt).toLocaleDateString()}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAae,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,qBAAqB;QACzB,MAAM,sBACH,IAAI,CAAC,CAAC;YACL,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,OAAO,IAAI,IAAI;QACjB,GACC,IAAI,CAAC,CAAC,OAAS,iBAAiB,OAChC,KAAK,CAAC,QAAQ,KAAK;IACxB;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,EAAE;IAEhC,qBACE,6LAAC,wIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC,mIAAA,CAAA,UAAU;;;;;gBAEV,cAAc,MAAM,KAAK,kBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,kBAClB,6LAAC;4BAEC,WAAU;4BACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE;;8CAE1D,6LAAC;oCACC,KAAK,EAAE,eAAe;oCACtB,KAAK,EAAE,WAAW,IAAI;oCACtB,WAAU;;;;;;gCAEX,EAAE,WAAW,kBACZ,6LAAC;oCAAE,WAAU;8CAA8B,EAAE,WAAW;;;;;;8CAE1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,CAAC,0BAA0B,EAC1C,EAAE,MAAM,KAAK,cAAc,8BAC3B,EAAE,MAAM,KAAK,aAAa,gCAC1B,EAAE,MAAM,KAAK,cAAc,kCAC3B,6BACA;sDACC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;;;;;;sDAErD,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK,EAAE,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;2BAtBxC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;AAgCzB;GAhEwB;KAAA", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}