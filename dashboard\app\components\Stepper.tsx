"use client";
import React from "react";

export interface Step {
  name: string;
  completed: boolean;
}

interface StepperProps {
  steps: Step[];
}

export default function Stepper({ steps }: StepperProps) {
  // Determine active step: first incomplete
  const firstIncomplete = steps.findIndex((s) => !s.completed);
  const activeIndex = firstIncomplete >= 0 ? firstIncomplete : steps.length - 1;
  return (
    <ol className="flex items-center justify-between w-full">
      {steps.map((step, idx) => {
        let status: 'done' | 'active' | 'pending' = 'pending';
        if (idx < activeIndex) status = 'done';
        else if (idx === activeIndex) status = 'active';
        const badgeClass =
          status === 'done'
            ? 'bg-green-500 text-white'
            : status === 'active'
            ? 'bg-blue-500 text-white'
            : 'bg-gray-300 text-gray-600';
        return (
          <li key={idx} className="flex-1">
            <div className="flex flex-col items-center">
              <div
                className={`rounded-full h-8 w-8 flex items-center justify-center mb-2 ${badgeClass}`}
              >
                {idx + 1}
              </div>
              <span className="text-sm text-center">{step.name}</span>
            </div>
          </li>
        );
      })}
    </ol>
  );
}
