"use client";
import { motion } from "framer-motion";
import { CheckIcon } from "@heroicons/react/24/solid";
import { cn } from "@/lib/utils";

export interface Step {
  name: string;
  completed: boolean;
}

interface StepperProps {
  steps: Step[];
  orientation?: "vertical" | "horizontal";
  showConnector?: boolean;
}

export default function Stepper({
  steps,
  orientation = "vertical",
  showConnector = true
}: StepperProps) {
  const currentStepIndex = steps.findIndex(step => !step.completed);
  const activeIndex = currentStepIndex === -1 ? steps.length - 1 : Math.max(0, currentStepIndex - 1);

  if (orientation === "horizontal") {
    return (
      <div className="flex items-center justify-between w-full">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center flex-1">
            <div className="flex flex-col items-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300",
                  step.completed
                    ? "bg-green-500 border-green-500 text-white shadow-lg"
                    : index === activeIndex
                    ? "bg-blue-500 border-blue-500 text-white shadow-lg animate-pulse"
                    : "bg-gray-100 border-gray-300 text-gray-500"
                )}
              >
                {step.completed ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <CheckIcon className="w-5 h-5" />
                  </motion.div>
                ) : (
                  <span>{index + 1}</span>
                )}
              </motion.div>
              <span
                className={cn(
                  "mt-2 text-xs font-medium text-center max-w-20",
                  step.completed
                    ? "text-green-600"
                    : index === activeIndex
                    ? "text-blue-600"
                    : "text-gray-500"
                )}
              >
                {step.name}
              </span>
            </div>

            {showConnector && index < steps.length - 1 && (
              <div className="flex-1 h-0.5 mx-4 bg-gray-200 relative">
                <motion.div
                  className="h-full bg-green-500"
                  initial={{ width: 0 }}
                  animate={{
                    width: step.completed ? "100%" : "0%"
                  }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {steps.map((step, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          className="flex items-start space-x-4"
        >
          <div className="flex flex-col items-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: index * 0.1 + 0.2 }}
              className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300",
                step.completed
                  ? "bg-green-500 border-green-500 text-white shadow-lg"
                  : index === activeIndex
                  ? "bg-blue-500 border-blue-500 text-white shadow-lg animate-pulse"
                  : "bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400"
              )}
            >
              {step.completed ? (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <CheckIcon className="w-5 h-5" />
                </motion.div>
              ) : (
                <span>{index + 1}</span>
              )}
            </motion.div>

            {showConnector && index < steps.length - 1 && (
              <div className="w-0.5 h-12 mt-2 bg-gray-200 dark:bg-gray-700 relative">
                <motion.div
                  className="w-full bg-green-500"
                  initial={{ height: 0 }}
                  animate={{
                    height: step.completed ? "100%" : "0%"
                  }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                />
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0 pt-1.5">
            <motion.h3
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 + 0.4 }}
              className={cn(
                "text-sm font-medium",
                step.completed
                  ? "text-green-600 dark:text-green-400"
                  : index === activeIndex
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-500 dark:text-gray-400"
              )}
            >
              {step.name}
            </motion.h3>

            {step.completed && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                transition={{ delay: index * 0.1 + 0.5 }}
                className="text-xs text-gray-500 dark:text-gray-400 mt-1"
              >
                Completed
              </motion.p>
            )}

            {index === activeIndex && !step.completed && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                transition={{ delay: index * 0.1 + 0.5 }}
                className="text-xs text-blue-500 dark:text-blue-400 mt-1"
              >
                In progress...
              </motion.p>
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );
}
