{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = db;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAE5D,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/api/prescriptions/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\nimport { db } from \"../../../../lib/db\";\nimport { cookies } from \"next/headers\";\nimport { jwtVerify } from \"jose\";\n\n// JWT Secret\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.NEXTAUTH_SECRET || \"development-secret\"\n);\n\n// Helper function to get user from token\nasync function getUserFromToken() {\n  try {\n    const cookieList = await cookies();\n    const sessionToken = cookieList.get('auth-token')?.value;\n\n    if (!sessionToken) {\n      return null;\n    }\n\n    const { payload } = await jwtVerify(sessionToken, JWT_SECRET);\n    return payload.user as { id: string; email: string; name: string };\n  } catch (error) {\n    return null;\n  }\n}\n\nexport async function GET(\n  request: Request,\n  { params }: { params: { id: string } }\n) {\n  const user = await getUserFromToken();\n  if (!user) {\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n\n  const prescription = await db.prescription.findFirst({\n    where: {\n      id: params.id,\n      userId: user.id // Ensure user can only access their own prescriptions\n    },\n    include: {\n      orders: true\n    }\n  });\n\n  if (!prescription) {\n    return NextResponse.json({ error: \"Prescription not found\" }, { status: 404 });\n  }\n\n  return NextResponse.json(prescription);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,aAAa;AACb,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,eAAe,IAAI;AAGjC,yCAAyC;AACzC,eAAe;IACb,IAAI;QACF,MAAM,aAAa,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC/B,MAAM,eAAe,WAAW,GAAG,CAAC,eAAe;QAEnD,IAAI,CAAC,cAAc;YACjB,OAAO;QACT;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAClD,OAAO,QAAQ,IAAI;IACrB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,eAAe,IACpB,OAAgB,EAChB,EAAE,MAAM,EAA8B;IAEtC,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,MAAM,eAAe,MAAM,2GAAA,CAAA,KAAE,CAAC,YAAY,CAAC,SAAS,CAAC;QACnD,OAAO;YACL,IAAI,OAAO,EAAE;YACb,QAAQ,KAAK,EAAE,CAAC,sDAAsD;QACxE;QACA,SAAS;YACP,QAAQ;QACV;IACF;IAEA,IAAI,CAAC,cAAc;QACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC9E;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B", "debugId": null}}]}