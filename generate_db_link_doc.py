import mysql.connector

DB_CONFIG = {
    'host': "localhost",
    'user': "root",
    'password': "",
    'database': "nplabs dev"
}

OUTPUT_FILE = "database_relationships.md"

def get_db_connection():
    return mysql.connector.connect(**DB_CONFIG)

def get_schema_info(cursor):
    db_name = DB_CONFIG['database']
    schema = {'tables': {}}

    # Get all tables
    cursor.execute(f"SELECT table_name FROM information_schema.tables WHERE table_schema = '{db_name}'")
    tables = [row[0] for row in cursor.fetchall()]

    for table_name in tables:
        schema['tables'][table_name] = {'columns': [], 'primary_keys': [], 'foreign_keys': []}

        # Get columns and primary keys
        cursor.execute(f"""
            SELECT column_name, data_type, column_key 
            FROM information_schema.columns 
            WHERE table_schema = '{db_name}' AND table_name = '{table_name}'
            ORDER BY ordinal_position
        """)
        for col_name, data_type, col_key in cursor.fetchall():
            schema['tables'][table_name]['columns'].append({'name': col_name, 'type': data_type})
            if col_key == 'PRI':
                schema['tables'][table_name]['primary_keys'].append(col_name)

        # Get foreign keys
        cursor.execute(f"""
            SELECT 
                kcu.column_name, 
                kcu.referenced_table_name, 
                kcu.referenced_column_name,
                kcu.constraint_name
            FROM information_schema.key_column_usage kcu
            JOIN information_schema.referential_constraints rc ON kcu.constraint_name = rc.constraint_name
            WHERE kcu.table_schema = '{db_name}' 
              AND kcu.table_name = '{table_name}'
              AND kcu.referenced_table_name IS NOT NULL
        """)
        for col, ref_table, ref_col, c_name in cursor.fetchall():
            schema['tables'][table_name]['foreign_keys'].append({
                'column': col,
                'references_table': ref_table,
                'references_column': ref_col,
                'constraint_name': c_name
            })
    return schema

def generate_markdown_doc(schema_info):
    md_content = f"# Database Schema Relationships: {DB_CONFIG['database']}\n\n"
    md_content += "This document outlines the tables in the database and how they are linked via primary and foreign keys.\n\n"

    for table_name, table_data in schema_info['tables'].items():
        md_content += f"## Table: `{table_name}`\n\n"
        
        # Columns
        md_content += "### Columns:\n"
        md_content += "| Column Name | Data Type |\n"
        md_content += "|-------------|-----------|\n"
        for col in table_data['columns']:
            md_content += f"| `{col['name']}` | {col['type']} |\n"
        md_content += "\n"

        # Primary Keys
        if table_data['primary_keys']:
            pk_string = ', '.join([f"`{pk}`" for pk in table_data['primary_keys']])
            md_content += f"**Primary Key(s):** {pk_string}\n\n"
        else:
            md_content += "**Primary Key(s):** None defined\n\n"

        # Foreign Keys
        if table_data['foreign_keys']:
            md_content += "### Foreign Key Relationships:\n"
            md_content += "| Local Column | References Table | References Column | Constraint Name |\n"
            md_content += "|--------------|------------------|-------------------|-----------------|\n"
            for fk in table_data['foreign_keys']:
                md_content += f"| `{fk['column']}` | `{fk['references_table']}` | `{fk['references_column']}` | `{fk['constraint_name']}` |\n"
            md_content += "\n"
        else:
            md_content += "**Foreign Key Relationships:** None explicitly defined.\n\n"
        
        # Attempt to infer relationships if no explicit FKs
        if not table_data['foreign_keys']:
            inferred_fks = []
            for col in table_data['columns']:
                col_name_lower = col['name'].lower()
                # Common naming patterns like 'other_table_id' or 'other_table_pk'
                if col_name_lower.endswith('_id') or col_name_lower.endswith('_pk'):
                    potential_ref_table_name = col_name_lower.replace('_id', '').replace('_pk', '') + 's' # simple pluralization
                    if potential_ref_table_name in schema_info['tables'] and 'id' in [c['name'].lower() for c in schema_info['tables'][potential_ref_table_name]['columns']]:
                        inferred_fks.append(f"- `{col['name']}` might reference `id` in `{potential_ref_table_name}` (inferred by naming convention)")
                    elif col_name_lower.replace('_id', '').replace('_pk', '') in schema_info['tables'] and 'id' in [c['name'].lower() for c in schema_info['tables'][col_name_lower.replace('_id', '').replace('_pk', '')]['columns']]: # check non-pluralized
                         inferred_fks.append(f"- `{col['name']}` might reference `id` in `{col_name_lower.replace('_id', '').replace('_pk', '')}` (inferred by naming convention)")
            if inferred_fks:
                md_content += "### Potential Inferred Relationships (based on naming conventions):\n"
                md_content += "\n".join(inferred_fks)
                md_content += "\n\n"

        md_content += "---\n\n"

    return md_content

def main():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        print(f"Connected to database. Fetching schema for '{DB_CONFIG['database']}'...")
        
        schema_info = get_schema_info(cursor)
        print("Schema information retrieved.")
        
        print("Generating Markdown document...")
        markdown_output = generate_markdown_doc(schema_info)
        
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write(markdown_output)
        print(f"Successfully generated database relationships document: {OUTPUT_FILE}")
        
    except mysql.connector.Error as err:
        print(f"Database error: {err}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    main()
