{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/UploadForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useState, FormEvent } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport toast from \"react-hot-toast\";\n\ninterface UploadFormProps {\n}\n\nexport default function UploadForm() {\n  const router = useRouter();\n  const [imageUrl, setImageUrl] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const res = await fetch(\"/api/prescriptions\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ prescriptionImg: imageUrl, description }),\n      });\n      if (!res.ok) throw new Error(\"Upload failed\");\n      toast.success(\"Prescription uploaded\");\n      setImageUrl(\"\");\n      setDescription(\"\");\n      router.refresh();\n    } catch (err) {\n      toast.error((err as Error).message || \"Error uploading\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"mb-6 p-4 border rounded\">\n      <div className=\"mb-2\">\n        <label className=\"block text-sm font-medium\">Image URL</label>\n        <input\n          type=\"text\"\n          value={imageUrl}\n          onChange={(e) => setImageUrl(e.target.value)}\n          required\n          className=\"mt-1 w-full border rounded px-2 py-1\"\n        />\n      </div>\n      <div className=\"mb-2\">\n        <label className=\"block text-sm font-medium\">Description</label>\n        <input\n          type=\"text\"\n          value={description}\n          onChange={(e) => setDescription(e.target.value)}\n          className=\"mt-1 w-full border rounded px-2 py-1\"\n        />\n      </div>\n      <button\n        type=\"submit\"\n        disabled={loading}\n        className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\"\n      >\n        {loading ? \"Uploading...\" : \"Upload\"}\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,sBAAsB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,iBAAiB;oBAAU;gBAAY;YAChE;YACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,YAAY;YACZ,eAAe;YACf,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,AAAC,IAAc,OAAO,IAAI;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA4B;;;;;;kCAC7C,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,QAAQ;wBACR,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA4B;;;;;;kCAC7C,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,UAAU,iBAAiB;;;;;;;;;;;;AAIpC;GAzDwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: string | Date) {\n  return new Date(date).toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatRelativeTime(date: string | Date) {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Just now'\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`\n  \n  return formatDate(date)\n}\n\nexport function getStatusColor(status: string) {\n  const colors = {\n    submitted: 'bg-blue-100 text-blue-800 border-blue-200',\n    review: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    approved: 'bg-green-100 text-green-800 border-green-200',\n    compounding: 'bg-purple-100 text-purple-800 border-purple-200',\n    quality_check: 'bg-orange-100 text-orange-800 border-orange-200',\n    packaging: 'bg-indigo-100 text-indigo-800 border-indigo-200',\n    shipped: 'bg-cyan-100 text-cyan-800 border-cyan-200',\n    delivered: 'bg-emerald-100 text-emerald-800 border-emerald-200'\n  }\n  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'\n}\n\nexport function getStatusProgress(status: string) {\n  const statusOrder = [\n    'submitted', 'review', 'approved', 'compounding', \n    'quality_check', 'packaging', 'shipped', 'delivered'\n  ]\n  const index = statusOrder.indexOf(status)\n  return index >= 0 ? ((index + 1) / statusOrder.length) * 100 : 0\n}\n\nexport function generateOrderId() {\n  return `RX-${Date.now().toString(36).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC,SAAS;QAChD,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;IACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IAC5E,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IAE/E,OAAO,WAAW;AACpB;AAEO,SAAS,eAAe,MAAc;IAC3C,MAAM,SAAS;QACb,WAAW;QACX,QAAQ;QACR,UAAU;QACV,aAAa;QACb,eAAe;QACf,WAAW;QACX,SAAS;QACT,WAAW;IACb;IACA,OAAO,MAAM,CAAC,OAA8B,IAAI;AAClD;AAEO,SAAS,kBAAkB,MAAc;IAC9C,MAAM,cAAc;QAClB;QAAa;QAAU;QAAY;QACnC;QAAiB;QAAa;QAAW;KAC1C;IACD,MAAM,QAAQ,YAAY,OAAO,CAAC;IAClC,OAAO,SAAS,IAAI,AAAC,CAAC,QAAQ,CAAC,IAAI,YAAY,MAAM,GAAI,MAAM;AACjE;AAEO,SAAS;IACd,OAAO,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,WAAW,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW,IAAI;AAC/G", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/ui/Button.tsx"], "sourcesContent": ["\"use client\";\nimport { forwardRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  loading?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"primary\", size = \"md\", loading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 focus:ring-blue-500 shadow-lg hover:shadow-xl\",\n      secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500\",\n      outline: \"border-2 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500\",\n      ghost: \"text-gray-600 hover:bg-gray-100 focus:ring-gray-500\",\n      danger: \"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl\"\n    };\n    \n    const sizes = {\n      sm: \"px-3 py-1.5 text-sm\",\n      md: \"px-4 py-2 text-sm\",\n      lg: \"px-6 py-3 text-base\"\n    };\n\n    return (\n      <motion.button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        disabled={disabled || loading}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </motion.button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAYA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACvB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/DashboardLayout.tsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useAuth } from \"../providers\";\nimport { useTheme } from \"./ThemeProvider\";\nimport { useRouter, usePathname } from \"next/navigation\";\nimport {\n  HomeIcon,\n  PlusIcon,\n  ChartBarIcon,\n  CogIcon,\n  BellIcon,\n  SunIcon,\n  MoonIcon,\n  UserCircleIcon,\n  LogOutIcon,\n  MenuIcon,\n  XMarkIcon\n} from \"@heroicons/react/24/outline\";\nimport { cn } from \"@/lib/utils\";\nimport Button from \"./ui/Button\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'New Prescription', href: '/dashboard/new', icon: PlusIcon },\n  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },\n  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },\n];\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { user, logout } = useAuth();\n  const { theme, toggleTheme } = useTheme();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [notifications] = useState(3); // Mock notification count\n\n  const handleLogout = async () => {\n    await logout();\n    router.push(\"/\");\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 transition-colors duration-300\">\n      {/* Mobile sidebar overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-40 lg:hidden\"\n          >\n            <div className=\"fixed inset-0 bg-black/50\" onClick={() => setSidebarOpen(false)} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.div\n        initial={false}\n        animate={{ x: sidebarOpen ? 0 : -320 }}\n        className={cn(\n          \"fixed inset-y-0 left-0 z-50 w-80 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-r border-gray-200 dark:border-gray-700 lg:translate-x-0 lg:static lg:inset-0\",\n          \"lg:w-64 xl:w-72\"\n        )}\n      >\n        <div className=\"flex h-full flex-col\">\n          {/* Logo */}\n          <div className=\"flex h-16 items-center justify-between px-6 border-b border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">NP</span>\n              </div>\n              <h1 className=\"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                NPLabs Pro\n              </h1>\n            </div>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800\"\n            >\n              <XMarkIcon className=\"w-5 h-5\" />\n            </button>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-6 space-y-2\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href;\n              return (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200\",\n                    isActive\n                      ? \"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg\"\n                      : \"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800\"\n                  )}\n                  whileHover={{ x: isActive ? 0 : 4 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <item.icon className=\"w-5 h-5 mr-3\" />\n                  {item.name}\n                </motion.a>\n              );\n            })}\n          </nav>\n\n          {/* User section */}\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <UserCircleIcon className=\"w-10 h-10 text-gray-400\" />\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                  {user?.name || 'User'}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                  {user?.email}\n                </p>\n              </div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={toggleTheme}\n                className=\"flex-1\"\n              >\n                {theme === 'light' ? <MoonIcon className=\"w-4 h-4\" /> : <SunIcon className=\"w-4 h-4\" />}\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleLogout}\n                className=\"flex-1\"\n              >\n                <LogOutIcon className=\"w-4 h-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 xl:pl-72\">\n        {/* Top bar */}\n        <header className=\"bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border-b border-gray-200 dark:border-gray-700 sticky top-0 z-30\">\n          <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n            <button\n              onClick={() => setSidebarOpen(true)}\n              className=\"lg:hidden p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800\"\n            >\n              <MenuIcon className=\"w-5 h-5\" />\n            </button>\n\n            <div className=\"flex items-center space-x-4\">\n              <motion.button\n                className=\"relative p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <BellIcon className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\n                {notifications > 0 && (\n                  <span className=\"absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {notifications}\n                  </span>\n                )}\n              </motion.button>\n            </div>\n          </div>\n        </header>\n\n        {/* Page content */}\n        <main className=\"py-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AApBA;;;;;;;;;AA0BA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAoB,MAAM;QAAkB,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACnE;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAY,MAAM;QAAuB,MAAM,gNAAA,CAAA,UAAO;IAAC;CAChE;AAEc,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD;IACtC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,0BAA0B;IAE/D,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;wBAA4B,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;0BAM/E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBAAE,GAAG,cAAc,IAAI,CAAC;gBAAI;gBACrC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yKACA;0BAGF,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAG,WAAU;sDAA+F;;;;;;;;;;;;8CAI/G,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0FACA,WACI,sEACA;oCAEN,YAAY;wCAAE,GAAG,WAAW,IAAI;oCAAE;oCAClC,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;;mCAZL,KAAK,IAAI;;;;;4BAepB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,MAAM;;;;;;;;;;;;;;;;;;8CAIb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,UAAU,wBAAU,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;qEAAe,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAE7E,6LAAC,qIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,yLAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,yLAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,gBAAgB,mBACf,6LAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASb,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAxJwB;;QACG,oHAAA,CAAA,UAAO;QACD,sIAAA,CAAA,WAAQ;QACxB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAJN", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState } from \"react\";\nimport UploadForm from \"@/app/components/UploadForm\";\nimport DashboardLayout from \"@/app/components/DashboardLayout\";\n\ninterface Prescription {\n  id: string;\n  prescriptionImg: string;\n  description?: string;\n  status: string;\n  createdAt: string;\n}\n\nexport default function DashboardPage() {\n  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);\n\n  const fetchPrescriptions = () => {\n    fetch(\"/api/prescriptions\")\n      .then((res) => {\n        if (!res.ok) throw new Error(\"Failed to fetch\");\n        return res.json();\n      })\n      .then((data) => setPrescriptions(data))\n      .catch(console.error);\n  };\n  useEffect(fetchPrescriptions, []);\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-4 max-w-7xl mx-auto\">\n        <div className=\"mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">My Prescriptions</h2>\n          <p className=\"text-gray-600\">Upload and track your prescription status</p>\n        </div>\n\n        <UploadForm />\n\n        {prescriptions.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-500\">No prescriptions yet. Upload your first prescription above!</p>\n          </div>\n        ) : (\n          <div className=\"mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {prescriptions.map((p) => (\n              <div\n                key={p.id}\n                className=\"bg-white border rounded-lg p-4 shadow hover:shadow-lg transition cursor-pointer\"\n                onClick={() => window.location.href = `/dashboard/${p.id}`}\n              >\n                <img\n                  src={p.prescriptionImg}\n                  alt={p.description || \"Prescription image\"}\n                  className=\"w-full h-48 object-cover rounded\"\n                />\n                {p.description && (\n                  <p className=\"mt-2 text-sm text-gray-700\">{p.description}</p>\n                )}\n                <div className=\"mt-2 flex justify-between items-center\">\n                  <span className={`px-2 py-1 text-xs rounded ${\n                    p.status === 'submitted' ? 'bg-blue-100 text-blue-800' :\n                    p.status === 'approved' ? 'bg-green-100 text-green-800' :\n                    p.status === 'delivered' ? 'bg-purple-100 text-purple-800' :\n                    'bg-gray-100 text-gray-800'\n                  }`}>\n                    {p.status.charAt(0).toUpperCase() + p.status.slice(1)}\n                  </span>\n                  <p className=\"text-xs text-gray-500\">\n                    {new Date(p.createdAt).toLocaleDateString()}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAae,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,qBAAqB;QACzB,MAAM,sBACH,IAAI,CAAC,CAAC;YACL,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,OAAO,IAAI,IAAI;QACjB,GACC,IAAI,CAAC,CAAC,OAAS,iBAAiB,OAChC,KAAK,CAAC,QAAQ,KAAK;IACxB;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,EAAE;IAEhC,qBACE,6LAAC,wIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC,mIAAA,CAAA,UAAU;;;;;gBAEV,cAAc,MAAM,KAAK,kBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,kBAClB,6LAAC;4BAEC,WAAU;4BACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE,EAAE,EAAE;;8CAE1D,6LAAC;oCACC,KAAK,EAAE,eAAe;oCACtB,KAAK,EAAE,WAAW,IAAI;oCACtB,WAAU;;;;;;gCAEX,EAAE,WAAW,kBACZ,6LAAC;oCAAE,WAAU;8CAA8B,EAAE,WAAW;;;;;;8CAE1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAW,CAAC,0BAA0B,EAC1C,EAAE,MAAM,KAAK,cAAc,8BAC3B,EAAE,MAAM,KAAK,aAAa,gCAC1B,EAAE,MAAM,KAAK,cAAc,kCAC3B,6BACA;sDACC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;;;;;;sDAErD,6LAAC;4CAAE,WAAU;sDACV,IAAI,KAAK,EAAE,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;2BAtBxC,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;AAgCzB;GAhEwB;KAAA", "debugId": null}}]}