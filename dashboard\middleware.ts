import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";

// Secret key for JWT verification
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || "development-secret"
);

// Helper function to verify JWT token
async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

export async function middleware(request: NextRequest) {
  // Get auth token from cookies
  const token = request.cookies.get('auth-token')?.value;
  
  // If no token or invalid token, redirect to login page
  if (!token) {
    const url = new URL("/", request.url);
    url.searchParams.set("callbackUrl", request.nextUrl.pathname);
    return NextResponse.redirect(url);
  }
  
  // Verify the token
  const payload = await verifyToken(token);
  if (!payload) {
    const url = new URL("/", request.url);
    url.searchParams.set("callbackUrl", request.nextUrl.pathname);
    return NextResponse.redirect(url);
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*"],
};
