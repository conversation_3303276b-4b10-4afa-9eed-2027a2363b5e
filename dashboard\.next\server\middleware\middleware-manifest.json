{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_65fe3fd6._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_12a3b8ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "x2B8J76hFhZboTFKgchAg0WeHWNefWaZNfWHNaxb8bM=", "__NEXT_PREVIEW_MODE_ID": "7c1eb27c9da62de08d6aaa86d7164013", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8d3fa39c2f08810013743d109b0d23dbaf3ba224be93ae1e06038907ce17bf4e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "49f5911fb21bfd95a4df426ca4b7188a0efd4a5ab1405c1599c3178295760ec3"}}}, "instrumentation": null, "functions": {}}