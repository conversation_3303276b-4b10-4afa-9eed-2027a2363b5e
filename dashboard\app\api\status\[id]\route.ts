import { NextResponse } from "next/server";
import { db } from "../../../../lib/db";
import { cookies } from "next/headers";
import { jwtVerify } from "jose";

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || "development-secret"
);

// Helper function to get user from token
async function getUserFromToken() {
  try {
    const cookieList = await cookies();
    const sessionToken = cookieList.get('auth-token')?.value;

    if (!sessionToken) {
      return null;
    }

    const { payload } = await jwtVerify(sessionToken, JWT_SECRET);
    return payload.user as { id: string; email: string; name: string };
  } catch (error) {
    return null;
  }
}

// Define the prescription processing pipeline steps
const PIPELINE_STEPS = [
  { name: "Submitted", key: "submitted" },
  { name: "Under Review", key: "review" },
  { name: "Approved", key: "approved" },
  { name: "Compounding", key: "compounding" },
  { name: "Quality Check", key: "quality_check" },
  { name: "Packaging", key: "packaging" },
  { name: "Shipped", key: "shipped" },
  { name: "Delivered", key: "delivered" }
];

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const user = await getUserFromToken();
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get the prescription to check its current status
  const prescription = await db.prescription.findFirst({
    where: {
      id: params.id,
      userId: user.id
    },
    include: {
      orders: true
    }
  });

  if (!prescription) {
    return NextResponse.json({ error: "Prescription not found" }, { status: 404 });
  }

  // Generate step status based on current prescription status
  const currentStatus = prescription.status;
  const currentStepIndex = PIPELINE_STEPS.findIndex(step => step.key === currentStatus);

  const steps = PIPELINE_STEPS.map((step, index) => ({
    name: step.name,
    completed: index <= currentStepIndex
  }));

  return NextResponse.json(steps);
}
