import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../../auth/[...nextauth]/route";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  // Placeholder pipeline stages
  const steps = [
    { name: "Received", completed: true },
    { name: "In Progress", completed: false },
    { name: "Quality Check", completed: false },
    { name: "Shipped", completed: false },
  ];
  return NextResponse.json(steps);
}
