
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  emailVerified: 'emailVerified',
  image: 'image',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Activity_logScalarFieldEnum = {
  id: 'id',
  prescription: 'prescription',
  ticket: 'ticket',
  user: 'user',
  activities: 'activities',
  old_data: 'old_data',
  current_data: 'current_data',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Activity_logsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  message: 'message',
  module_name: 'module_name',
  url: 'url',
  request: 'request',
  method: 'method',
  ip: 'ip',
  agent: 'agent',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  color: 'color'
};

exports.Prisma.ClinicScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  clinic_id: 'clinic_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.EmailtextsScalarFieldEnum = {
  id: 'id',
  email: 'email',
  subject: 'subject',
  greeting: 'greeting',
  body: 'body',
  havecta: 'havecta',
  ctatext: 'ctatext',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Failed_jobsScalarFieldEnum = {
  id: 'id',
  uuid: 'uuid',
  connection: 'connection',
  queue: 'queue',
  payload: 'payload',
  exception: 'exception',
  failed_at: 'failed_at'
};

exports.Prisma.FeedbackScalarFieldEnum = {
  id: 'id',
  feedback_id: 'feedback_id',
  content: 'content',
  attachment: 'attachment',
  user: 'user',
  status: 'status',
  archive_at: 'archive_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.MigrationsScalarFieldEnum = {
  id: 'id',
  migration: 'migration',
  batch: 'batch'
};

exports.Prisma.NotificationsScalarFieldEnum = {
  id: 'id',
  type: 'type',
  notifiable_type: 'notifiable_type',
  notifiable_id: 'notifiable_id',
  data: 'data',
  read_at: 'read_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.OrdersScalarFieldEnum = {
  id: 'id',
  order_id: 'order_id',
  user_id: 'user_id',
  prescription_id: 'prescription_id',
  particulars: 'particulars',
  paymentlink: 'paymentlink',
  payment_intent_id: 'payment_intent_id',
  paymentstatus: 'paymentstatus',
  paymentprof: 'paymentprof',
  trackingid: 'trackingid',
  trackinglink: 'trackinglink',
  comments: 'comments',
  deliverstatus: 'deliverstatus',
  created_at: 'created_at',
  updated_at: 'updated_at',
  payment_done: 'payment_done',
  medication_created: 'medication_created',
  payment_link_created_at: 'payment_link_created_at',
  delivered_date: 'delivered_date'
};

exports.Prisma.PatientScalarFieldEnum = {
  user_id: 'user_id',
  patient_id: 'patient_id'
};

exports.Prisma.PermissionsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Personal_access_tokensScalarFieldEnum = {
  id: 'id',
  tokenable_type: 'tokenable_type',
  tokenable_id: 'tokenable_id',
  name: 'name',
  token: 'token',
  abilities: 'abilities',
  last_used_at: 'last_used_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PrescriberScalarFieldEnum = {
  user_id: 'user_id',
  prescriber_id: 'prescriber_id'
};

exports.Prisma.Prescription_queriesScalarFieldEnum = {
  id: 'id',
  prescription_id: 'prescription_id',
  patient_id: 'patient_id',
  status: 'status',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Prescription_query_responsesScalarFieldEnum = {
  id: 'id',
  prescription_query_id: 'prescription_query_id',
  sender_id: 'sender_id',
  message: 'message',
  status: 'status',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PrescriptionsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  prescriber_id: 'prescriber_id',
  pharmacy_id: 'pharmacy_id',
  clinic_id: 'clinic_id',
  description: 'description',
  prescription_img: 'prescription_img',
  numberofrefill: 'numberofrefill',
  amount: 'amount',
  status: 'status',
  comments: 'comments',
  approved_by_user: 'approved_by_user',
  order_id: 'order_id',
  re_order_id: 're_order_id',
  merge_to: 'merge_to',
  merge_with: 'merge_with',
  payment_type: 'payment_type',
  archive_at: 'archive_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PriorityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  color: 'color'
};

exports.Prisma.Role_userScalarFieldEnum = {
  user_id: 'user_id',
  role_id: 'role_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.RolesScalarFieldEnum = {
  id: 'id',
  role: 'role',
  slug: 'slug',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Roles_permissionsScalarFieldEnum = {
  role_id: 'role_id',
  permission_id: 'permission_id'
};

exports.Prisma.StatusScalarFieldEnum = {
  id: 'id',
  name: 'name',
  color: 'color'
};

exports.Prisma.Ticket_commentsScalarFieldEnum = {
  id: 'id',
  content: 'content',
  html: 'html',
  user_id: 'user_id',
  ticket_id: 'ticket_id',
  recent: 'recent',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.TicketsScalarFieldEnum = {
  id: 'id',
  track_id: 'track_id',
  subject: 'subject',
  content: 'content',
  html: 'html',
  ticketimg: 'ticketimg',
  status_id: 'status_id',
  priority_id: 'priority_id',
  user_id: 'user_id',
  staff_id: 'staff_id',
  category_id: 'category_id',
  archive_at: 'archive_at',
  created_at: 'created_at',
  updated_at: 'updated_at',
  completed_at: 'completed_at'
};

exports.Prisma.UsersScalarFieldEnum = {
  id: 'id',
  dob: 'dob',
  fname: 'fname',
  lname: 'lname',
  email: 'email',
  profileimg: 'profileimg',
  email_verified_at: 'email_verified_at',
  password: 'password',
  country: 'country',
  city: 'city',
  address: 'address',
  postcode: 'postcode',
  phone: 'phone',
  company: 'company',
  extension: 'extension',
  medical_license_no: 'medical_license_no',
  country_medical_license: 'country_medical_license',
  by_affiliate: 'by_affiliate',
  approved_at: 'approved_at',
  remember_token: 'remember_token',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Users_permissionsScalarFieldEnum = {
  user_id: 'user_id',
  permission_id: 'permission_id'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  image: 'image'
};

exports.Prisma.activity_logOrderByRelevanceFieldEnum = {
  activities: 'activities',
  old_data: 'old_data',
  current_data: 'current_data'
};

exports.Prisma.activity_logsOrderByRelevanceFieldEnum = {
  message: 'message',
  module_name: 'module_name',
  url: 'url',
  request: 'request',
  method: 'method',
  ip: 'ip',
  agent: 'agent'
};

exports.Prisma.categoryOrderByRelevanceFieldEnum = {
  name: 'name',
  color: 'color'
};

exports.Prisma.emailtextsOrderByRelevanceFieldEnum = {
  email: 'email',
  subject: 'subject',
  greeting: 'greeting',
  body: 'body',
  ctatext: 'ctatext'
};

exports.Prisma.failed_jobsOrderByRelevanceFieldEnum = {
  uuid: 'uuid',
  connection: 'connection',
  queue: 'queue',
  payload: 'payload',
  exception: 'exception'
};

exports.Prisma.feedbackOrderByRelevanceFieldEnum = {
  feedback_id: 'feedback_id',
  content: 'content',
  attachment: 'attachment',
  status: 'status'
};

exports.Prisma.migrationsOrderByRelevanceFieldEnum = {
  migration: 'migration'
};

exports.Prisma.notificationsOrderByRelevanceFieldEnum = {
  id: 'id',
  type: 'type',
  notifiable_type: 'notifiable_type',
  data: 'data'
};

exports.Prisma.ordersOrderByRelevanceFieldEnum = {
  order_id: 'order_id',
  particulars: 'particulars',
  paymentlink: 'paymentlink',
  payment_intent_id: 'payment_intent_id',
  paymentstatus: 'paymentstatus',
  paymentprof: 'paymentprof',
  trackingid: 'trackingid',
  trackinglink: 'trackinglink',
  comments: 'comments',
  deliverstatus: 'deliverstatus'
};

exports.Prisma.permissionsOrderByRelevanceFieldEnum = {
  name: 'name',
  slug: 'slug'
};

exports.Prisma.personal_access_tokensOrderByRelevanceFieldEnum = {
  tokenable_type: 'tokenable_type',
  name: 'name',
  token: 'token',
  abilities: 'abilities'
};

exports.Prisma.prescription_query_responsesOrderByRelevanceFieldEnum = {
  message: 'message'
};

exports.Prisma.prescriptionsOrderByRelevanceFieldEnum = {
  description: 'description',
  prescription_img: 'prescription_img',
  numberofrefill: 'numberofrefill',
  amount: 'amount',
  status: 'status',
  comments: 'comments',
  approved_by_user: 'approved_by_user',
  merge_to: 'merge_to',
  payment_type: 'payment_type'
};

exports.Prisma.priorityOrderByRelevanceFieldEnum = {
  name: 'name',
  color: 'color'
};

exports.Prisma.rolesOrderByRelevanceFieldEnum = {
  role: 'role',
  slug: 'slug'
};

exports.Prisma.statusOrderByRelevanceFieldEnum = {
  name: 'name',
  color: 'color'
};

exports.Prisma.ticket_commentsOrderByRelevanceFieldEnum = {
  content: 'content',
  html: 'html'
};

exports.Prisma.ticketsOrderByRelevanceFieldEnum = {
  track_id: 'track_id',
  subject: 'subject',
  content: 'content',
  html: 'html',
  ticketimg: 'ticketimg'
};

exports.Prisma.usersOrderByRelevanceFieldEnum = {
  fname: 'fname',
  lname: 'lname',
  email: 'email',
  profileimg: 'profileimg',
  password: 'password',
  country: 'country',
  city: 'city',
  address: 'address',
  postcode: 'postcode',
  phone: 'phone',
  company: 'company',
  medical_license_no: 'medical_license_no',
  country_medical_license: 'country_medical_license',
  remember_token: 'remember_token'
};


exports.Prisma.ModelName = {
  User: 'User',
  activity_log: 'activity_log',
  activity_logs: 'activity_logs',
  category: 'category',
  clinic: 'clinic',
  emailtexts: 'emailtexts',
  failed_jobs: 'failed_jobs',
  feedback: 'feedback',
  migrations: 'migrations',
  notifications: 'notifications',
  orders: 'orders',
  patient: 'patient',
  permissions: 'permissions',
  personal_access_tokens: 'personal_access_tokens',
  prescriber: 'prescriber',
  prescription_queries: 'prescription_queries',
  prescription_query_responses: 'prescription_query_responses',
  prescriptions: 'prescriptions',
  priority: 'priority',
  role_user: 'role_user',
  roles: 'roles',
  roles_permissions: 'roles_permissions',
  status: 'status',
  ticket_comments: 'ticket_comments',
  tickets: 'tickets',
  users: 'users',
  users_permissions: 'users_permissions'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
