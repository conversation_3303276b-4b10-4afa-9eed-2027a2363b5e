import bcrypt from "bcryptjs";
import { db } from "../../../../lib/db";
import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { SignJWT, jwtVerify } from "jose";

// Secret key for JWT signing
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || "development-secret"
);

// Helper function to create JWT token
async function createToken(payload: any) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('30d')
    .sign(JWT_SECRET);
}

// Helper function to verify JWT token
async function verifyToken(token: string) {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    return null;
  }
}

export async function GET(request: Request) {
  try {
    // Get the session token from cookies
    const cookieList = cookies();
    const sessionToken = cookieList.get('auth-token')?.value;
    
    if (!sessionToken) {
      return NextResponse.json({ user: null });
    }
    
    // Verify the token
    const payload = await verifyToken(sessionToken);
    
    if (!payload) {
      return NextResponse.json({ user: null });
    }
    
    // Return the user data
    return NextResponse.json({ user: payload.user });
  } catch (error) {
    console.error('Session error:', error);
    return NextResponse.json({ user: null });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    // Handle login
    if (body.email && body.password) {
      const user = await db.user.findUnique({
        where: { email: body.email },
        select: { id: true, email: true, name: true, password: true }
      });

      if (!user) {
        return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
      }

      const isValid = await bcrypt.compare(body.password, user.password);
      if (!isValid) {
        return NextResponse.json({ error: "Invalid credentials" }, { status: 401 });
      }

      // Create user data for token
      const userData = {
        id: user.id.toString(),
        name: user.name,
        email: user.email
      };

      // Create JWT token
      const token = await createToken({ user: userData });

      // Set cookie
      const response = NextResponse.json({ user: userData });
      response.cookies.set('auth-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 30 * 24 * 60 * 60, // 30 days
        path: '/'
      });

      return response;
    }

    // Handle logout
    if (body.logout) {
      const response = NextResponse.json({ success: true });
      response.cookies.set('auth-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 0,
        path: '/'
      });
      return response;
    }

    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  } catch (error) {
    console.error("Auth error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
