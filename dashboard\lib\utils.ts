import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function formatRelativeTime(date: string | Date) {
  const now = new Date()
  const targetDate = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return formatDate(date)
}

export function getStatusColor(status: string) {
  const colors = {
    submitted: 'bg-blue-100 text-blue-800 border-blue-200',
    review: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    approved: 'bg-green-100 text-green-800 border-green-200',
    compounding: 'bg-purple-100 text-purple-800 border-purple-200',
    quality_check: 'bg-orange-100 text-orange-800 border-orange-200',
    packaging: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    shipped: 'bg-cyan-100 text-cyan-800 border-cyan-200',
    delivered: 'bg-emerald-100 text-emerald-800 border-emerald-200'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
}

export function getStatusProgress(status: string) {
  const statusOrder = [
    'submitted', 'review', 'approved', 'compounding', 
    'quality_check', 'packaging', 'shipped', 'delivered'
  ]
  const index = statusOrder.indexOf(status)
  return index >= 0 ? ((index + 1) / statusOrder.length) * 100 : 0
}

export function generateOrderId() {
  return `RX-${Date.now().toString(36).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`
}
