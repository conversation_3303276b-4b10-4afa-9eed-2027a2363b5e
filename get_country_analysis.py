import mysql.connector
import json

def get_unique_countries():
    """Retrieve all unique country names from the users table"""
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="nplabs dev"
        )
        cursor = conn.cursor(dictionary=True)
        
        # Get count of users per country
        cursor.execute("""
            SELECT 
                country, 
                COUNT(*) as user_count 
            FROM users 
            WHERE country IS NOT NULL 
            GROUP BY country 
            ORDER BY user_count DESC, country
        """)
        
        results = cursor.fetchall()
        return results
        
    except Exception as e:
        print(f"Error: {e}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals() and conn.is_connected():
            conn.close()

def save_country_analysis():
    """Save country analysis to a JSON file"""
    countries = get_unique_countries()
    
    # Save to file for analysis
    with open('country_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(countries, f, indent=2, ensure_ascii=False)
    
    print(f"Analysis complete. Found {len(countries)} unique country names.")
    print("Results saved to country_analysis.json")

if __name__ == "__main__":
    save_country_analysis()
