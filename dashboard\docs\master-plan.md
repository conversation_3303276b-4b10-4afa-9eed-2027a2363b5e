# Dashboard Master Plan

## 1. Project Structure

```
dashboard/
├── app/               # Next.js App Router source
│   ├── layout.tsx     # Root layout
│   ├── page.tsx       # Homepage (login)
│   └── dashboard/     # Protected dashboard routes
│       ├── page.tsx   # Dashboard overview
│       └── [id]/      # Prescription detail pages
├── prisma/            # Prisma schema & migrations
│   └── schema.prisma  # Data model + introspection
├── public/            # Static assets
├── styles/            # Global CSS (Tailwind config)
├── docs/              # Project documentation
│   └── master-plan.md # This file
├── next.config.js     # Next.js config
├── package.json       # Dependencies & scripts
└── tsconfig.json      # TypeScript config
```

## 2. Tech Stack

- **Next.js** (React + App Router + API Routes)
- **TypeScript** for type safety
- **Tailwind CSS** for utility-first styling
- **Prisma** ORM (MySQL introspection + client)
- **NextAuth.js** for authentication
- **MySQL** production database

## 3. Prisma Setup

1. `npx prisma init --datasource-provider mysql`
2. Set `DATABASE_URL` in `.env` to connect to `nplabs2` schema
3. `npx prisma db pull` to introspect existing tables
4. `npx prisma generate` to build the client

## 4. Authentication

- Use **NextAuth.js** with Credentials Provider (email/password)
- Store sessions in JWT (default) or database for refresh
- Protect `app/dashboard` routes via middleware
- Use `@next-auth/prisma-adapter` to integrate NextAuth with Prisma models
- Create `app/api/auth/[...nextauth]/route.ts` for NextAuth configuration with adapter
- Add `middleware.ts` at project root to enforce authentication on `/dashboard` routes

## 5. API Routes

| Path                         | Method | Purpose                                   |
|------------------------------|--------|-------------------------------------------|
| `POST /api/auth/[...nextauth]` | --   | Built-in NextAuth                          |
| `GET /api/prescriptions`     | GET    | List user’s prescriptions                 |
| `POST /api/prescriptions`    | POST   | Upload new prescription                   |
| `GET /api/prescriptions/:id` | GET    | Fetch prescription details & status       |
| `GET /api/status/:id`        | GET    | Fetch status pipeline data               |

### Step 5.1: Scaffolding API Endpoints
1. Create directory `app/api/prescriptions`
2. Implement `GET /api/prescriptions` in `app/api/prescriptions/route.ts` using NextAuth session and Prisma client
3. Implement remaining routes (`POST`, `GET by id`, status)
4. Create directory `app/api/status` and implement `GET /api/status/[id]` in `app/api/status/[id]/route.ts` returning pipeline stages

## 6. Frontend Pages & Components

- **Login Page** (`/`) with email/password form
- **Dashboard** (`/dashboard`) showing prescription cards
- **Dashboard Detail** (`/dashboard/[id]`) showing a Stepper component for compounding stages
- **UI Components**:
  - `Card` for list items
  - `Stepper` (Headless UI or custom)
  - `UploadForm` with drag-and-drop

### Step 6.1: Detail Page & Stepper
1. Create `app/dashboard/[id]/page.tsx` to fetch `/api/prescriptions/:id` and `/api/status/:id`
2. Render `<Stepper />` with stages and completion state

## 7. Styling & UX

- Mobile responsive (flex/grid)
- Color-coded steps: pending (gray), active (blue), done (green)
- Toast notifications for upload/status updates

## 8. Deployment

- **Vercel**: Link repo, set `DATABASE_URL` in Env.
- Preview deploys on PRs.
- Automatic builds & rollbacks.

## 9. Next Steps

1. Scaffold Prisma & introspect schema
2. Configure NextAuth and middleware
3. Create core API Routes
4. Build Dashboard UI and Steps
5. Test flows locally
6. Push & deploy to Vercel
