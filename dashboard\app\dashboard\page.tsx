"use client";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import UploadForm from "@/app/components/UploadForm";
import DashboardLayout from "@/app/components/DashboardLayout";
import PrescriptionCard from "@/app/components/PrescriptionCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/Card";
import {
  PlusIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline";

interface Prescription {
  id: string;
  prescriptionImg: string;
  description?: string;
  status: string;
  createdAt: string;
}

export default function DashboardPage() {
  const router = useRouter();
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUploadForm, setShowUploadForm] = useState(false);

  const fetchPrescriptions = async () => {
    try {
      setLoading(true);
      const res = await fetch("/api/prescriptions");
      if (!res.ok) throw new Error("Failed to fetch");
      const data = await res.json();
      setPrescriptions(data);
    } catch (error) {
      console.error("Error fetching prescriptions:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrescriptions();
  }, []);

  // Calculate stats
  const stats = {
    total: prescriptions.length,
    pending: prescriptions.filter(p => ['submitted', 'review'].includes(p.status)).length,
    inProgress: prescriptions.filter(p => ['approved', 'compounding', 'quality_check', 'packaging'].includes(p.status)).length,
    completed: prescriptions.filter(p => ['shipped', 'delivered'].includes(p.status)).length,
  };

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Manage and track your prescription orders
            </p>
          </div>

          <motion.button
            onClick={() => setShowUploadForm(!showUploadForm)}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            New Prescription
          </motion.button>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            { label: 'Total', value: stats.total, icon: ChartBarIcon, color: 'blue' },
            { label: 'Pending', value: stats.pending, icon: ClockIcon, color: 'yellow' },
            { label: 'In Progress', value: stats.inProgress, icon: ClockIcon, color: 'purple' },
            { label: 'Completed', value: stats.completed, icon: CheckCircleIcon, color: 'green' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        {stat.label}
                      </p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">
                        {stat.value}
                      </p>
                    </div>
                    <div className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}>
                      <stat.icon className={`w-6 h-6 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Upload Form */}
        {showUploadForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <UploadForm onSuccess={() => {
              fetchPrescriptions();
              setShowUploadForm(false);
            }} />
          </motion.div>
        )}

        {/* Prescriptions Grid */}
        {loading ? (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 dark:bg-gray-700 rounded-xl h-64"></div>
              </div>
            ))}
          </div>
        ) : prescriptions.length === 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                <PlusIcon className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No prescriptions yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Get started by uploading your first prescription
              </p>
              <motion.button
                onClick={() => setShowUploadForm(true)}
                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <PlusIcon className="w-5 h-5 mr-2" />
                Upload Prescription
              </motion.button>
            </div>
          </motion.div>
        ) : (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {prescriptions.map((prescription, index) => (
              <PrescriptionCard
                key={prescription.id}
                prescription={prescription}
                index={index}
                onClick={() => router.push(`/dashboard/${prescription.id}`)}
              />
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
