"use client";
import { useEffect, useState } from "react";
import UploadForm from "@/app/components/UploadForm";

interface Prescription {
  id: bigint;
  prescription_img: string;
  description?: string;
  created_at: string;
}

export default function DashboardPage() {
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);

  const fetchPrescriptions = () => {
    fetch("/api/prescriptions")
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fetch");
        return res.json();
      })
      .then((data) => setPrescriptions(data))
      .catch(console.error);
  };
  useEffect(fetchPrescriptions, []);

  return (
    <div className="p-4 max-w-7xl mx-auto">
      <UploadForm />
      <div className="mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {prescriptions.map((p) => (
          <div
            key={p.id.toString()}
            className="border rounded p-4 shadow hover:shadow-lg transition"
          >
            <img
              src={p.prescription_img}
              alt={p.description || "Prescription image"}
              className="w-full h-48 object-cover rounded"
            />
            {p.description && (
              <p className="mt-2 text-sm text-gray-700">{p.description}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {new Date(p.created_at).toLocaleDateString()}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
