"use client";
import { useEffect, useState } from "react";
import UploadForm from "@/app/components/UploadForm";
import DashboardLayout from "@/app/components/DashboardLayout";

interface Prescription {
  id: string;
  prescriptionImg: string;
  description?: string;
  status: string;
  createdAt: string;
}

export default function DashboardPage() {
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);

  const fetchPrescriptions = () => {
    fetch("/api/prescriptions")
      .then((res) => {
        if (!res.ok) throw new Error("Failed to fetch");
        return res.json();
      })
      .then((data) => setPrescriptions(data))
      .catch(console.error);
  };
  useEffect(fetchPrescriptions, []);

  return (
    <DashboardLayout>
      <div className="p-4 max-w-7xl mx-auto">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">My Prescriptions</h2>
          <p className="text-gray-600">Upload and track your prescription status</p>
        </div>

        <UploadForm />

        {prescriptions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No prescriptions yet. Upload your first prescription above!</p>
          </div>
        ) : (
          <div className="mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {prescriptions.map((p) => (
              <div
                key={p.id}
                className="bg-white border rounded-lg p-4 shadow hover:shadow-lg transition cursor-pointer"
                onClick={() => window.location.href = `/dashboard/${p.id}`}
              >
                <img
                  src={p.prescriptionImg}
                  alt={p.description || "Prescription image"}
                  className="w-full h-48 object-cover rounded"
                />
                {p.description && (
                  <p className="mt-2 text-sm text-gray-700">{p.description}</p>
                )}
                <div className="mt-2 flex justify-between items-center">
                  <span className={`px-2 py-1 text-xs rounded ${
                    p.status === 'submitted' ? 'bg-blue-100 text-blue-800' :
                    p.status === 'approved' ? 'bg-green-100 text-green-800' :
                    p.status === 'delivered' ? 'bg-purple-100 text-purple-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {p.status.charAt(0).toUpperCase() + p.status.slice(1)}
                  </span>
                  <p className="text-xs text-gray-500">
                    {new Date(p.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
