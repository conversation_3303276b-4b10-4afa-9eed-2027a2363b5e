{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/UploadForm.tsx"], "sourcesContent": ["\"use client\";\nimport { useState, FormEvent } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport toast from \"react-hot-toast\";\n\ninterface UploadFormProps {\n}\n\nexport default function UploadForm() {\n  const router = useRouter();\n  const [imageUrl, setImageUrl] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      const res = await fetch(\"/api/prescriptions\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ prescription_img: imageUrl, description }),\n      });\n      if (!res.ok) throw new Error(\"Upload failed\");\n      toast.success(\"Prescription uploaded\");\n      setImageUrl(\"\");\n      setDescription(\"\");\n      router.refresh();\n    } catch (err) {\n      toast.error((err as Error).message || \"Error uploading\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"mb-6 p-4 border rounded\">\n      <div className=\"mb-2\">\n        <label className=\"block text-sm font-medium\">Image URL</label>\n        <input\n          type=\"text\"\n          value={imageUrl}\n          onChange={(e) => setImageUrl(e.target.value)}\n          required\n          className=\"mt-1 w-full border rounded px-2 py-1\"\n        />\n      </div>\n      <div className=\"mb-2\">\n        <label className=\"block text-sm font-medium\">Description</label>\n        <input\n          type=\"text\"\n          value={description}\n          onChange={(e) => setDescription(e.target.value)}\n          className=\"mt-1 w-full border rounded px-2 py-1\"\n        />\n      </div>\n      <button\n        type=\"submit\"\n        disabled={loading}\n        className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\"\n      >\n        {loading ? \"Uploading...\" : \"Upload\"}\n      </button>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,sBAAsB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,kBAAkB;oBAAU;gBAAY;YACjE;YACA,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd,YAAY;YACZ,eAAe;YACf,OAAO,OAAO;QAChB,EAAE,OAAO,KAAK;YACZ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC,AAAC,IAAc,OAAO,IAAI;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA4B;;;;;;kCAC7C,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC3C,QAAQ;wBACR,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA4B;;;;;;kCAC7C,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBACC,MAAK;gBACL,UAAU;gBACV,WAAU;0BAET,UAAU,iBAAiB;;;;;;;;;;;;AAIpC;GAzDwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState } from \"react\";\nimport UploadForm from \"@/app/components/UploadForm\";\n\ninterface Prescription {\n  id: bigint;\n  prescription_img: string;\n  description?: string;\n  created_at: string;\n}\n\nexport default function DashboardPage() {\n  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);\n\n  const fetchPrescriptions = () => {\n    fetch(\"/api/prescriptions\")\n      .then((res) => {\n        if (!res.ok) throw new Error(\"Failed to fetch\");\n        return res.json();\n      })\n      .then((data) => setPrescriptions(data))\n      .catch(console.error);\n  };\n  useEffect(fetchPrescriptions, []);\n\n  return (\n    <div className=\"p-4 max-w-7xl mx-auto\">\n      <UploadForm />\n      <div className=\"mt-6 grid sm:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {prescriptions.map((p) => (\n          <div\n            key={p.id.toString()}\n            className=\"border rounded p-4 shadow hover:shadow-lg transition\"\n          >\n            <img\n              src={p.prescription_img}\n              alt={p.description || \"Prescription image\"}\n              className=\"w-full h-48 object-cover rounded\"\n            />\n            {p.description && (\n              <p className=\"mt-2 text-sm text-gray-700\">{p.description}</p>\n            )}\n            <p className=\"mt-1 text-xs text-gray-500\">\n              {new Date(p.created_at).toLocaleDateString()}\n            </p>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAWe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,qBAAqB;QACzB,MAAM,sBACH,IAAI,CAAC,CAAC;YACL,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;YAC7B,OAAO,IAAI,IAAI;QACjB,GACC,IAAI,CAAC,CAAC,OAAS,iBAAiB,OAChC,KAAK,CAAC,QAAQ,KAAK;IACxB;IACA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,EAAE;IAEhC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BACX,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,kBAClB,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCACC,KAAK,EAAE,gBAAgB;gCACvB,KAAK,EAAE,WAAW,IAAI;gCACtB,WAAU;;;;;;4BAEX,EAAE,WAAW,kBACZ,6LAAC;gCAAE,WAAU;0CAA8B,EAAE,WAAW;;;;;;0CAE1D,6LAAC;gCAAE,WAAU;0CACV,IAAI,KAAK,EAAE,UAAU,EAAE,kBAAkB;;;;;;;uBAZvC,EAAE,EAAE,CAAC,QAAQ;;;;;;;;;;;;;;;;AAmB9B;GAvCwB;KAAA", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}