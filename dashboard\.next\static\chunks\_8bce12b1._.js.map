{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/Stepper.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\n\nexport interface Step {\n  name: string;\n  completed: boolean;\n}\n\ninterface StepperProps {\n  steps: Step[];\n}\n\nexport default function Stepper({ steps }: StepperProps) {\n  // Determine active step: first incomplete\n  const firstIncomplete = steps.findIndex((s) => !s.completed);\n  const activeIndex = firstIncomplete >= 0 ? firstIncomplete : steps.length - 1;\n  return (\n    <ol className=\"flex items-center justify-between w-full\">\n      {steps.map((step, idx) => {\n        let status: 'done' | 'active' | 'pending' = 'pending';\n        if (idx < activeIndex) status = 'done';\n        else if (idx === activeIndex) status = 'active';\n        const badgeClass =\n          status === 'done'\n            ? 'bg-green-500 text-white'\n            : status === 'active'\n            ? 'bg-blue-500 text-white'\n            : 'bg-gray-300 text-gray-600';\n        return (\n          <li key={idx} className=\"flex-1\">\n            <div className=\"flex flex-col items-center\">\n              <div\n                className={`rounded-full h-8 w-8 flex items-center justify-center mb-2 ${badgeClass}`}\n              >\n                {idx + 1}\n              </div>\n              <span className=\"text-sm text-center\">{step.name}</span>\n            </div>\n          </li>\n        );\n      })}\n    </ol>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAYe,SAAS,QAAQ,EAAE,KAAK,EAAgB;IACrD,0CAA0C;IAC1C,MAAM,kBAAkB,MAAM,SAAS,CAAC,CAAC,IAAM,CAAC,EAAE,SAAS;IAC3D,MAAM,cAAc,mBAAmB,IAAI,kBAAkB,MAAM,MAAM,GAAG;IAC5E,qBACE,6LAAC;QAAG,WAAU;kBACX,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,IAAI,SAAwC;YAC5C,IAAI,MAAM,aAAa,SAAS;iBAC3B,IAAI,QAAQ,aAAa,SAAS;YACvC,MAAM,aACJ,WAAW,SACP,4BACA,WAAW,WACX,2BACA;YACN,qBACE,6LAAC;gBAAa,WAAU;0BACtB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC,2DAA2D,EAAE,YAAY;sCAEpF,MAAM;;;;;;sCAET,6LAAC;4BAAK,WAAU;sCAAuB,KAAK,IAAI;;;;;;;;;;;;eAP3C;;;;;QAWb;;;;;;AAGN;KA/BwB", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/components/DashboardLayout.tsx"], "sourcesContent": ["\"use client\";\nimport { useAuth } from \"../providers\";\nimport { useRouter } from \"next/navigation\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    await logout();\n    router.push(\"/\");\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Navigation Header */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                NPLabs Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">\n                Welcome, {user?.name || user?.email}\n              </span>\n              <button\n                onClick={handleLogout}\n                className=\"text-sm text-gray-500 hover:text-gray-700\"\n              >\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"py-6\">\n        {children}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;AAFA;;;AAQe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAItD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAwB;4CAC5B,MAAM,QAAQ,MAAM;;;;;;;kDAEhC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GAzCwB;;QACG,oHAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/dashboard/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState } from \"react\";\nimport toast from \"react-hot-toast\";\nimport { usePara<PERSON>, useRouter } from \"next/navigation\";\nimport Stepper, { Step } from \"@/app/components/Stepper\";\nimport DashboardLayout from \"@/app/components/DashboardLayout\";\n\ninterface Prescription {\n  id: string;\n  prescriptionImg: string;\n  description?: string;\n  status: string;\n  createdAt: string;\n}\n\nexport default function PrescriptionDetailPage() {\n  const params = useParams();\n  const router = useRouter();\n  const id = params.id;\n  const [prescription, setPrescription] = useState<Prescription | null>(null);\n  const [steps, setSteps] = useState<Step[]>([]);\n\n  useEffect(() => {\n    if (!id) return;\n    fetch(`/api/prescriptions/${id}`)\n      .then((res) => {\n        if (!res.ok) throw new Error(\"Prescription not found\");\n        return res.json();\n      })\n      .then((data) => setPrescription(data))\n      .catch(() => { toast.error(\"Prescription not found\"); router.push(\"/dashboard\"); });\n\n    fetch(`/api/status/${id}`)\n      .then((res) => res.json())\n      .then((data: Step[]) => setSteps(data))\n      .catch(() => toast.error(\"Failed to load status\"));\n  }, [id, router]);\n\n  if (!prescription) return <p>Loading...</p>;\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6 max-w-3xl mx-auto space-y-6\">\n        <div className=\"flex items-center space-x-4 mb-6\">\n          <button\n            type=\"button\"\n            onClick={() => router.back()}\n            className=\"text-blue-600 hover:underline flex items-center\"\n          >\n            &larr; Back to Dashboard\n          </button>\n        </div>\n\n        <h2 className=\"text-2xl font-semibold text-gray-900\">Prescription Details</h2>\n\n        <div className=\"bg-white p-6 rounded-lg shadow space-y-6\">\n          <img\n            src={prescription.prescriptionImg}\n            alt={prescription.description || \"Prescription\"}\n            className=\"w-full max-w-2xl mx-auto rounded-lg\"\n          />\n\n          {prescription.description && (\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Description</h3>\n              <p className=\"text-gray-700\">{prescription.description}</p>\n            </div>\n          )}\n\n          <div className=\"bg-gray-50 p-6 rounded-lg\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Processing Status</h3>\n            <Stepper steps={steps} />\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;AALA;;;;;;AAee,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,IAAI;YACT,MAAM,CAAC,mBAAmB,EAAE,IAAI,EAC7B,IAAI;oDAAC,CAAC;oBACL,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM;oBAC7B,OAAO,IAAI,IAAI;gBACjB;mDACC,IAAI;oDAAC,CAAC,OAAS,gBAAgB;mDAC/B,KAAK;oDAAC;oBAAQ,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;oBAA2B,OAAO,IAAI,CAAC;gBAAe;;YAEnF,MAAM,CAAC,YAAY,EAAE,IAAI,EACtB,IAAI;oDAAC,CAAC,MAAQ,IAAI,IAAI;mDACtB,IAAI;oDAAC,CAAC,OAAiB,SAAS;mDAChC,KAAK;oDAAC,IAAM,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;;QAC7B;2CAAG;QAAC;QAAI;KAAO;IAEf,IAAI,CAAC,cAAc,qBAAO,6LAAC;kBAAE;;;;;;IAE7B,qBACE,6LAAC,wIAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,IAAI;wBAC1B,WAAU;kCACX;;;;;;;;;;;8BAKH,6LAAC;oBAAG,WAAU;8BAAuC;;;;;;8BAErD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,KAAK,aAAa,eAAe;4BACjC,KAAK,aAAa,WAAW,IAAI;4BACjC,WAAU;;;;;;wBAGX,aAAa,WAAW,kBACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAiB,aAAa,WAAW;;;;;;;;;;;;sCAI1D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC,gIAAA,CAAA,UAAO;oCAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GA9DwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}