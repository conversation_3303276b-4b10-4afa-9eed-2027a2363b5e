# Database Schema Relationships: nplabs dev

This document outlines the tables in the database and how they are linked via primary and foreign keys.

## Table: `activity_log`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `prescription` | bigint |
| `ticket` | bigint |
| `user` | bigint |
| `activities` | longtext |
| `old_data` | longtext |
| `current_data` | longtext |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

### Foreign Key Relationships:
| Local Column | References Table | References Column | Constraint Name |
|--------------|------------------|-------------------|-----------------|
| `prescription` | `prescriptions` | `id` | `activity_log_prescription_foreign` |
| `ticket` | `tickets` | `id` | `activity_log_ticket_foreign` |
| `user` | `users` | `id` | `activity_log_user_foreign` |

---

## Table: `activity_logs`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `user_id` | int |
| `message` | varchar |
| `module_name` | varchar |
| `url` | varchar |
| `request` | varchar |
| `method` | varchar |
| `ip` | varchar |
| `agent` | varchar |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)

---

## Table: `category`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `name` | varchar |
| `color` | varchar |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `clinic`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `user_id` | bigint |
| `clinic_id` | bigint |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

### Foreign Key Relationships:
| Local Column | References Table | References Column | Constraint Name |
|--------------|------------------|-------------------|-----------------|
| `clinic_id` | `users` | `id` | `clinic_clinic_id_foreign` |
| `user_id` | `users` | `id` | `clinic_user_id_foreign` |

---

## Table: `emailtexts`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `email` | varchar |
| `subject` | varchar |
| `greeting` | varchar |
| `body` | longtext |
| `havecta` | tinyint |
| `ctatext` | varchar |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `failed_jobs`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `uuid` | varchar |
| `connection` | text |
| `queue` | text |
| `payload` | longtext |
| `exception` | longtext |
| `failed_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `feedback`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `feedback_id` | varchar |
| `content` | longtext |
| `attachment` | varchar |
| `user` | bigint |
| `status` | varchar |
| `archive_at` | timestamp |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

### Foreign Key Relationships:
| Local Column | References Table | References Column | Constraint Name |
|--------------|------------------|-------------------|-----------------|
| `user` | `users` | `id` | `feedback_user_foreign` |

---

## Table: `migrations`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | int |
| `migration` | varchar |
| `batch` | int |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `notifications`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | char |
| `type` | varchar |
| `notifiable_type` | varchar |
| `notifiable_id` | bigint |
| `data` | text |
| `read_at` | timestamp |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `orders`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `order_id` | varchar |
| `user_id` | bigint |
| `prescription_id` | bigint |
| `particulars` | longtext |
| `paymentlink` | varchar |
| `payment_intent_id` | varchar |
| `paymentstatus` | varchar |
| `paymentprof` | varchar |
| `trackingid` | varchar |
| `trackinglink` | varchar |
| `comments` | varchar |
| `deliverstatus` | varchar |
| `created_at` | timestamp |
| `updated_at` | timestamp |
| `payment_done` | date |
| `medication_created` | date |
| `payment_link_created_at` | date |
| `delivered_date` | date |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `order_id` might reference `id` in `orders` (inferred by naming convention)
- `user_id` might reference `id` in `users` (inferred by naming convention)
- `prescription_id` might reference `id` in `prescriptions` (inferred by naming convention)

---

## Table: `password_resets`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `email` | varchar |
| `token` | varchar |
| `created_at` | timestamp |

**Primary Key(s):** None defined

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `patient`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `user_id` | bigint |
| `patient_id` | bigint |

**Primary Key(s):** `user_id`, `patient_id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)

---

## Table: `permissions`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `name` | varchar |
| `slug` | varchar |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `personal_access_tokens`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `tokenable_type` | varchar |
| `tokenable_id` | bigint |
| `name` | varchar |
| `token` | varchar |
| `abilities` | text |
| `last_used_at` | timestamp |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `prescriber`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `user_id` | bigint |
| `prescriber_id` | bigint |

**Primary Key(s):** `user_id`, `prescriber_id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)

---

## Table: `prescriptions`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `user_id` | bigint |
| `prescriber_id` | bigint |
| `pharmacy_id` | int |
| `clinic_id` | bigint |
| `description` | varchar |
| `prescription_img` | varchar |
| `numberofrefill` | varchar |
| `amount` | varchar |
| `status` | varchar |
| `comments` | varchar |
| `approved_by_user` | varchar |
| `order_id` | int |
| `re_order_id` | int |
| `merge_to` | longtext |
| `merge_with` | int |
| `payment_type` | varchar |
| `archive_at` | timestamp |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)
- `clinic_id` might reference `id` in `clinic` (inferred by naming convention)
- `order_id` might reference `id` in `orders` (inferred by naming convention)

---

## Table: `prescription_queries`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `prescription_id` | bigint |
| `patient_id` | bigint |
| `status` | tinyint |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

### Foreign Key Relationships:
| Local Column | References Table | References Column | Constraint Name |
|--------------|------------------|-------------------|-----------------|
| `patient_id` | `users` | `id` | `prescription_queries_patient_id_foreign` |
| `prescription_id` | `prescriptions` | `id` | `prescription_queries_prescription_id_foreign` |

---

## Table: `prescription_query_responses`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `prescription_query_id` | bigint |
| `sender_id` | bigint |
| `message` | longtext |
| `status` | tinyint |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

### Foreign Key Relationships:
| Local Column | References Table | References Column | Constraint Name |
|--------------|------------------|-------------------|-----------------|
| `prescription_query_id` | `prescription_queries` | `id` | `prescription_query_responses_prescription_query_id_foreign` |

---

## Table: `priority`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `name` | varchar |
| `color` | varchar |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `roles`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `role` | varchar |
| `slug` | varchar |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `roles_permissions`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `role_id` | bigint |
| `permission_id` | bigint |

**Primary Key(s):** `role_id`, `permission_id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `role_id` might reference `id` in `roles` (inferred by naming convention)
- `permission_id` might reference `id` in `permissions` (inferred by naming convention)

---

## Table: `role_user`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `user_id` | bigint |
| `role_id` | bigint |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `user_id`, `role_id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)
- `role_id` might reference `id` in `roles` (inferred by naming convention)

---

## Table: `status`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `name` | varchar |
| `color` | varchar |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `tickets`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `track_id` | varchar |
| `subject` | varchar |
| `content` | longtext |
| `html` | longtext |
| `ticketimg` | varchar |
| `status_id` | bigint |
| `priority_id` | bigint |
| `user_id` | bigint |
| `staff_id` | bigint |
| `category_id` | bigint |
| `archive_at` | timestamp |
| `created_at` | timestamp |
| `updated_at` | timestamp |
| `completed_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `status_id` might reference `id` in `status` (inferred by naming convention)
- `priority_id` might reference `id` in `priority` (inferred by naming convention)
- `user_id` might reference `id` in `users` (inferred by naming convention)
- `category_id` might reference `id` in `category` (inferred by naming convention)

---

## Table: `ticket_comments`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `content` | longtext |
| `html` | longtext |
| `user_id` | bigint |
| `ticket_id` | bigint |
| `recent` | tinyint |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)
- `ticket_id` might reference `id` in `tickets` (inferred by naming convention)

---

## Table: `users`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `dob` | date |
| `fname` | varchar |
| `lname` | varchar |
| `email` | varchar |
| `profileimg` | varchar |
| `email_verified_at` | timestamp |
| `password` | varchar |
| `country` | varchar |
| `city` | varchar |
| `address` | varchar |
| `postcode` | varchar |
| `phone` | varchar |
| `company` | varchar |
| `extension` | bigint |
| `medical_license_no` | varchar |
| `country_medical_license` | varchar |
| `by_affiliate` | bigint |
| `approved_at` | timestamp |
| `remember_token` | varchar |
| `created_at` | timestamp |
| `updated_at` | timestamp |

**Primary Key(s):** `id`

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `users_country_backup`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `country` | varchar |
| `email` | varchar |
| `created_at` | timestamp |

**Primary Key(s):** None defined

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `users_country_backup_20250516214627`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `id` | bigint |
| `country` | varchar |

**Primary Key(s):** None defined

**Foreign Key Relationships:** None explicitly defined.

---

## Table: `users_permissions`

### Columns:
| Column Name | Data Type |
|-------------|-----------|
| `user_id` | bigint |
| `permission_id` | bigint |

**Primary Key(s):** `user_id`, `permission_id`

**Foreign Key Relationships:** None explicitly defined.

### Potential Inferred Relationships (based on naming conventions):
- `user_id` might reference `id` in `users` (inferred by naming convention)
- `permission_id` might reference `id` in `permissions` (inferred by naming convention)

---

