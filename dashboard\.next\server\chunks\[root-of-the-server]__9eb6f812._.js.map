{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = db;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAE5D,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import bcrypt from \"bcryptjs\";\nimport { db } from \"../../../../lib/db\";\nimport { NextResponse } from \"next/server\";\nimport { cookies } from \"next/headers\";\nimport { SignJWT, jwtVerify } from \"jose\";\n\n// Secret key for JWT signing\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.NEXTAUTH_SECRET || \"development-secret\"\n);\n\n// Helper function to create JWT token\nasync function createToken(payload: any) {\n  return await new SignJWT(payload)\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('30d')\n    .sign(JWT_SECRET);\n}\n\n// Helper function to verify JWT token\nasync function verifyToken(token: string) {\n  try {\n    const { payload } = await jwtVerify(token, JWT_SECRET);\n    return payload;\n  } catch (error) {\n    return null;\n  }\n}\n\nexport async function GET(request: Request) {\n  try {\n    // Get the session token from cookies\n    const cookieList = cookies();\n    const sessionToken = cookieList.get('auth-token')?.value;\n    \n    if (!sessionToken) {\n      return NextResponse.json({ user: null });\n    }\n    \n    // Verify the token\n    const payload = await verifyToken(sessionToken);\n    \n    if (!payload) {\n      return NextResponse.json({ user: null });\n    }\n    \n    // Return the user data\n    return NextResponse.json({ user: payload.user });\n  } catch (error) {\n    console.error('Session error:', error);\n    return NextResponse.json({ user: null });\n  }\n}\n\nexport async function POST(request: Request) {\n  try {\n    const body = await request.json();\n    \n    // Handle login\n    if (body.email && body.password) {\n      const user = await db.user.findUnique({\n        where: { email: body.email },\n        select: { id: true, email: true, name: true, password: true }\n      });\n\n      if (!user) {\n        return NextResponse.json({ error: \"Invalid credentials\" }, { status: 401 });\n      }\n\n      const isValid = await bcrypt.compare(body.password, user.password);\n      if (!isValid) {\n        return NextResponse.json({ error: \"Invalid credentials\" }, { status: 401 });\n      }\n\n      // Create user data for token\n      const userData = {\n        id: user.id.toString(),\n        name: user.name,\n        email: user.email\n      };\n\n      // Create JWT token\n      const token = await createToken({ user: userData });\n\n      // Set cookie\n      const response = NextResponse.json({ user: userData });\n      response.cookies.set('auth-token', token, {\n        httpOnly: true,\n        secure: process.env.NODE_ENV === 'production',\n        maxAge: 30 * 24 * 60 * 60, // 30 days\n        path: '/'\n      });\n\n      return response;\n    }\n\n    // Handle logout\n    if (body.logout) {\n      const response = NextResponse.json({ success: true });\n      response.cookies.set('auth-token', '', {\n        httpOnly: true,\n        secure: process.env.NODE_ENV === 'production',\n        maxAge: 0,\n        path: '/'\n      });\n      return response;\n    }\n\n    return NextResponse.json({ error: \"Invalid request\" }, { status: 400 });\n  } catch (error) {\n    console.error(\"Auth error:\", error);\n    return NextResponse.json({ error: \"Internal server error\" }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,6BAA6B;AAC7B,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,eAAe,IAAI;AAGjC,sCAAsC;AACtC,eAAe,YAAY,OAAY;IACrC,OAAO,MAAM,IAAI,uJAAA,CAAA,UAAO,CAAC,SACtB,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,OAClB,IAAI,CAAC;AACV;AAEA,sCAAsC;AACtC,eAAe,YAAY,KAAa;IACtC,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,qCAAqC;QACrC,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QACzB,MAAM,eAAe,WAAW,GAAG,CAAC,eAAe;QAEnD,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAK;QACxC;QAEA,mBAAmB;QACnB,MAAM,UAAU,MAAM,YAAY;QAElC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAK;QACxC;QAEA,uBAAuB;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM,QAAQ,IAAI;QAAC;IAChD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAK;IACxC;AACF;AAEO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,eAAe;QACf,IAAI,KAAK,KAAK,IAAI,KAAK,QAAQ,EAAE;YAC/B,MAAM,OAAO,MAAM,2GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;gBACpC,OAAO;oBAAE,OAAO,KAAK,KAAK;gBAAC;gBAC3B,QAAQ;oBAAE,IAAI;oBAAM,OAAO;oBAAM,MAAM;oBAAM,UAAU;gBAAK;YAC9D;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAsB,GAAG;oBAAE,QAAQ;gBAAI;YAC3E;YAEA,MAAM,UAAU,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,KAAK,QAAQ;YACjE,IAAI,CAAC,SAAS;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAsB,GAAG;oBAAE,QAAQ;gBAAI;YAC3E;YAEA,6BAA6B;YAC7B,MAAM,WAAW;gBACf,IAAI,KAAK,EAAE,CAAC,QAAQ;gBACpB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;YACnB;YAEA,mBAAmB;YACnB,MAAM,QAAQ,MAAM,YAAY;gBAAE,MAAM;YAAS;YAEjD,aAAa;YACb,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAS;YACpD,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;gBACxC,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,QAAQ,KAAK,KAAK,KAAK;gBACvB,MAAM;YACR;YAEA,OAAO;QACT;QAEA,gBAAgB;QAChB,IAAI,KAAK,MAAM,EAAE;YACf,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;YAAK;YACnD,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI;gBACrC,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,QAAQ;gBACR,MAAM;YACR;YACA,OAAO;QACT;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAkB,GAAG;YAAE,QAAQ;QAAI;IACvE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/api/prescriptions/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth/next\";\nimport { authOptions } from \"../auth/[...nextauth]/route\";\nimport { PrismaClient } from \"@prisma/client\";\n\nconst prisma = new PrismaClient();\n\nexport async function GET(request: Request) {\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.id) {\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userId = Number(session.user.id);\n  const prescriptions = await prisma.prescriptions.findMany({\n    where: { user_id: BigInt(userId) },\n    orderBy: { created_at: \"desc\" },\n  });\n  return NextResponse.json(prescriptions);\n}\n\nexport async function POST(request: Request) {\n  const session = await getServerSession(authOptions);\n  if (!session?.user?.id) {\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userId = Number(session.user.id);\n  const { prescription_img, description } = await request.json();\n  const prescription = await prisma.prescriptions.create({\n    data: {\n      user_id: BigInt(userId),\n      prescription_img,\n      description,\n    },\n  });\n  return NextResponse.json(prescription);\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;;;AAEA;AACA;;;;;AAEA,MAAM,SAAS,IAAI,6HAAA,CAAA,eAAY;AAExB,eAAe,IAAI,OAAgB;IACxC,MAAM,UAAU,MAAM,iBAAiB,mJAAA,CAAA,cAAW;IAClD,IAAI,CAAC,SAAS,MAAM,IAAI;QACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IACA,MAAM,SAAS,OAAO,QAAQ,IAAI,CAAC,EAAE;IACrC,MAAM,gBAAgB,MAAM,OAAO,aAAa,CAAC,QAAQ,CAAC;QACxD,OAAO;YAAE,SAAS,OAAO;QAAQ;QACjC,SAAS;YAAE,YAAY;QAAO;IAChC;IACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAEO,eAAe,KAAK,OAAgB;IACzC,MAAM,UAAU,MAAM,iBAAiB,mJAAA,CAAA,cAAW;IAClD,IAAI,CAAC,SAAS,MAAM,IAAI;QACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IACA,MAAM,SAAS,OAAO,QAAQ,IAAI,CAAC,EAAE;IACrC,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;IAC5D,MAAM,eAAe,MAAM,OAAO,aAAa,CAAC,MAAM,CAAC;QACrD,MAAM;YACJ,SAAS,OAAO;YAChB;YACA;QACF;IACF;IACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B", "debugId": null}}]}