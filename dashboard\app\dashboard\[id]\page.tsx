"use client";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Stepper, { Step } from "@/app/components/Stepper";
import DashboardLayout from "@/app/components/DashboardLayout";

interface Prescription {
  id: string;
  prescriptionImg: string;
  description?: string;
  status: string;
  createdAt: string;
}

export default function PrescriptionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id;
  const [prescription, setPrescription] = useState<Prescription | null>(null);
  const [steps, setSteps] = useState<Step[]>([]);

  useEffect(() => {
    if (!id) return;
    fetch(`/api/prescriptions/${id}`)
      .then((res) => {
        if (!res.ok) throw new Error("Prescription not found");
        return res.json();
      })
      .then((data) => setPrescription(data))
      .catch(() => { toast.error("Prescription not found"); router.push("/dashboard"); });

    fetch(`/api/status/${id}`)
      .then((res) => res.json())
      .then((data: Step[]) => setSteps(data))
      .catch(() => toast.error("Failed to load status"));
  }, [id, router]);

  if (!prescription) return <p>Loading...</p>;

  return (
    <DashboardLayout>
      <div className="p-6 max-w-3xl mx-auto space-y-6">
        <div className="flex items-center space-x-4 mb-6">
          <button
            type="button"
            onClick={() => router.back()}
            className="text-blue-600 hover:underline flex items-center"
          >
            &larr; Back to Dashboard
          </button>
        </div>

        <h2 className="text-2xl font-semibold text-gray-900">Prescription Details</h2>

        <div className="bg-white p-6 rounded-lg shadow space-y-6">
          <img
            src={prescription.prescriptionImg}
            alt={prescription.description || "Prescription"}
            className="w-full max-w-2xl mx-auto rounded-lg"
          />

          {prescription.description && (
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
              <p className="text-gray-700">{prescription.description}</p>
            </div>
          )}

          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Status</h3>
            <Stepper steps={steps} />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
