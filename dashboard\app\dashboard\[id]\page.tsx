"use client";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import toast from "react-hot-toast";
import { useParams, useRouter } from "next/navigation";
import {
  ArrowLeftIcon,
  CalendarIcon,
  ClockIcon,
  DocumentTextIcon,
  EyeIcon
} from "@heroicons/react/24/outline";
import Stepper, { Step } from "@/app/components/Stepper";
import DashboardLayout from "@/app/components/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/Card";
import Button from "@/app/components/ui/Button";
import { formatDate, formatRelativeTime, getStatusColor } from "@/lib/utils";

interface Prescription {
  id: string;
  prescriptionImg: string;
  description?: string;
  status: string;
  createdAt: string;
}

export default function PrescriptionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id;
  const [prescription, setPrescription] = useState<Prescription | null>(null);
  const [steps, setSteps] = useState<Step[]>([]);

  useEffect(() => {
    if (!id) return;
    fetch(`/api/prescriptions/${id}`)
      .then((res) => {
        if (!res.ok) throw new Error("Prescription not found");
        return res.json();
      })
      .then((data) => setPrescription(data))
      .catch(() => { toast.error("Prescription not found"); router.push("/dashboard"); });

    fetch(`/api/status/${id}`)
      .then((res) => res.json())
      .then((data: Step[]) => setSteps(data))
      .catch(() => toast.error("Failed to load status"));
  }, [id, router]);

  if (!prescription) return <p>Loading...</p>;

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8 max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center space-x-4"
        >
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>Back to Dashboard</span>
          </Button>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Left Column - Prescription Image */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <EyeIcon className="w-5 h-5 text-blue-600" />
                  <span>Prescription Image</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative group">
                  <img
                    src={prescription.prescriptionImg}
                    alt={prescription.description || "Prescription"}
                    className="w-full rounded-lg shadow-lg transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 rounded-lg" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Right Column - Details & Status */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Prescription Info */}
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DocumentTextIcon className="w-5 h-5 text-purple-600" />
                  <span>Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</span>
                  <span className={`px-3 py-1 text-xs font-medium rounded-full border ${getStatusColor(prescription.status)}`}>
                    {prescription.status.charAt(0).toUpperCase() + prescription.status.slice(1).replace('_', ' ')}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Created</span>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatDate(prescription.createdAt)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {formatRelativeTime(prescription.createdAt)}
                    </div>
                  </div>
                </div>

                {prescription.description && (
                  <div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400 block mb-2">Description</span>
                    <p className="text-sm text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                      {prescription.description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  Schedule Consultation
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <ClockIcon className="w-4 h-4 mr-2" />
                  Request Update
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Processing Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ClockIcon className="w-5 h-5 text-green-600" />
                <span>Processing Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Stepper steps={steps} orientation="horizontal" />
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}
