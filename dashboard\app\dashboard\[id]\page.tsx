"use client";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Stepper, { Step } from "@/app/components/Stepper";

interface Prescription {
  id: string;
  prescription_img: string;
  description?: string;
  created_at: string;
}

export default function PrescriptionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id;
  const [prescription, setPrescription] = useState<Prescription | null>(null);
  const [steps, setSteps] = useState<Step[]>([]);

  useEffect(() => {
    if (!id) return;
    fetch(`/api/prescriptions/${id}`)
      .then((res) => {
        if (!res.ok) throw new Error("Prescription not found");
        return res.json();
      })
      .then((data) => setPrescription(data))
      .catch(() => { toast.error("Prescription not found"); router.push("/dashboard"); });

    fetch(`/api/status/${id}`)
      .then((res) => res.json())
      .then((data: Step[]) => setSteps(data))
      .catch(() => toast.error("Failed to load status"));
  }, [id, router]);

  if (!prescription) return <p>Loading...</p>;

  return (
    <main className="p-6 max-w-3xl mx-auto space-y-6">
      <h2 className="text-2xl font-semibold">Prescription Details</h2>
      <div className="bg-white p-4 rounded shadow space-y-4">
        <button onClick={() => router.back()} className="text-blue-600 hover:underline">
          &larr; Back
        </button>
        <img
          src={prescription.prescription_img}
          alt={prescription.description || "Prescription"}
          className="w-full rounded"
        />
        {prescription.description && (
          <p className="text-center text-gray-700">{prescription.description}</p>
        )}
        <div className="mt-6 bg-gray-50 p-4 rounded">
          <Stepper steps={steps} />
        </div>
      </div>
    </main>
  );
}
