# Contributing Guidelines: Prescription Dashboard

## Code of Conduct
We expect all contributors to adhere to our code of conduct, which promotes a respectful and inclusive environment for everyone.

## Development Process

### 1. Branching Strategy
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Feature branches
- `bugfix/*`: Bug fix branches
- `hotfix/*`: Emergency fixes for production

### 2. Pull Request Process
1. Create a branch from `develop` for your changes
2. Make your changes with appropriate tests
3. Ensure all tests pass locally
4. Submit a PR to the `develop` branch
5. Request review from at least one team member
6. Address any feedback from reviewers
7. Once approved, maintainers will merge your PR

### 3. Commit Message Format
We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

Types:
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code changes that neither fix bugs nor add features
- `perf`: Performance improvements
- `test`: Adding or correcting tests
- `chore`: Changes to the build process or auxiliary tools

Example:
```
feat(auth): implement JWT token refresh

- Add token refresh endpoint
- Update middleware to handle expired tokens
- Add tests for refresh flow

Closes #123
```

## Code Standards

### TypeScript
- Use TypeScript for all new code
- Maintain strict type checking
- Avoid using `any` type when possible
- Use interfaces for object shapes

### React & Next.js
- Use functional components with hooks
- Follow the Next.js App Router patterns
- Keep components small and focused
- Use client/server component directives appropriately

### CSS & Styling
- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Maintain consistent spacing and sizing

### Testing
- Write tests for all new features
- Maintain test coverage above 80%
- Test both happy paths and edge cases

## Documentation
- Update relevant documentation when making changes
- Document all public APIs and components
- Include JSDoc comments for functions and interfaces
- Keep the README and other docs up to date

## Review Process
- Code reviews focus on:
  - Correctness
  - Performance
  - Security
  - Maintainability
  - Test coverage
- Be respectful and constructive in reviews
- Address all review comments before merge

## Getting Help
- Ask questions in the #dashboard-dev Slack channel
- Tag relevant team members for specific questions
- Check existing documentation and issues first