import mysql.connector

def connect_to_mysql():
    try:
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",  # Default XAMPP password is empty
            database="nplabs dev"
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Error connecting to MySQL: {err}")
        return None

def get_database_info(connection):
    cursor = connection.cursor(dictionary=True)
    
    # Get list of tables
    cursor.execute("SHOW TABLES")
    tables = [list(table.values())[0] for table in cursor.fetchall()]
    
    db_info = {}
    
    for table in tables:
        # Get column information
        cursor.execute(f"SHOW COLUMNS FROM `{table}`")
        columns = cursor.fetchall()
        
        # Get row count
        cursor.execute(f"SELECT COUNT(*) as count FROM `{table}`")
        row_count = cursor.fetchone()['count']
        
        # Get sample data
        sample_data = []
        if row_count > 0:
            cursor.execute(f"SELECT * FROM `{table}` LIMIT 3")
            sample_data = cursor.fetchall()
        
        db_info[table] = {
            'columns': [col['Field'] for col in columns],
            'column_details': {col['Field']: f"{col['Type']} {'NULL' if col['Null'] == 'YES' else 'NOT NULL'}" for col in columns},
            'row_count': row_count,
            'sample_data': sample_data[:3]  # Get first 3 rows max
        }
    
    cursor.close()
    return db_info

def display_database_info(db_info):
    print(f"\n{'='*80}")
    print(f"DATABASE SCHEMA OVERVIEW")
    print(f"Tables found: {len(db_info)}")
    print("="*80)
    
    for table, info in db_info.items():
        print(f"\n{' TABLE: ' + table + ' ':-^80}")
        print(f"Columns: {len(info['columns'])}")
        print(f"Rows: {info['row_count']}")
        
        print("\nCOLUMNS:")
        for col in info['columns']:
            print(f"  - {col}: {info['column_details'][col]}")
        
        if info['sample_data']:
            print("\nSAMPLE DATA (first 3 rows):")
            for i, row in enumerate(info['sample_data'], 1):
                print(f"\n  Row {i}:")
                for col in info['columns']:
                    value = row[col]
                    if value is not None and len(str(value)) > 50:
                        value = str(value)[:47] + "..."
                    print(f"    {col}: {value}")
        else:
            print("\nNo data in table.")
        
        print("\n" + "-"*80)

def main():
    print("Connecting to MySQL database 'nplabs dev'...")
    connection = connect_to_mysql()
    
    if connection:
        try:
            print("Connected successfully!")
            print("\nFetching database information...")
            db_info = get_database_info(connection)
            
            if not db_info:
                print("No tables found in the database.")
            else:
                display_database_info(db_info)
                
        except mysql.connector.Error as err:
            print(f"\nMySQL Error: {err}")
        except Exception as e:
            print(f"\nError: {e}")
        finally:
            connection.close()
            print("\nConnection closed.")
    else:
        print("Failed to connect to MySQL. Please check if MySQL server is running.")

if __name__ == "__main__":
    main()
