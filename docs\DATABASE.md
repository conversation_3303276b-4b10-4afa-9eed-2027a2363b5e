# Database Documentation: Prescription Dashboard

## Overview
The application uses a MySQL database with Prisma ORM for data access. The schema is based on an existing database structure that has been introspected using Prisma.

## Connection Configuration
Database connection is configured via the `DATABASE_URL` environment variable in the format:
```
mysql://username:password@host:port/database
```

## Key Tables

### `User`
Stores user authentication and profile information.

| Column | Type | Description |
|--------|------|-------------|
| `id` | String (cuid) | Primary key |
| `name` | String | User's full name |
| `email` | String | User's email (unique) |
| `password` | String | Hashed password |
| `emailVerified` | DateTime | When email was verified |
| `image` | String | Profile image URL |
| `createdAt` | DateTime | Account creation timestamp |
| `updatedAt` | DateTime | Last update timestamp |

### `prescriptions`
Stores prescription information.

| Column | Type | Description |
|--------|------|-------------|
| `id` | BigInt | Primary key |
| `user_id` | BigInt | Foreign key to user |
| `prescriber_id` | BigInt | Doctor/prescriber ID |
| `pharmacy_id` | Int | Pharmacy location ID |
| `clinic_id` | BigInt | Clinic ID |
| `description` | String | Prescription description |
| `prescription_img` | String | Image URL |
| `numberofrefill` | String | Number of refills allowed |
| `amount` | String | Medication amount |
| `status` | String | Current status |
| `comments` | String | Additional notes |
| `approved_by_user` | String | Approval status |
| `created_at` | DateTime | Creation timestamp |
| `updated_at` | DateTime | Last update timestamp |

### `orders`
Stores order information related to prescriptions.

| Column | Type | Description |
|--------|------|-------------|
| `id` | BigInt | Primary key |
| `order_id` | String | Order identifier |
| `user_id` | BigInt | User ID |
| `prescription_id` | BigInt | Related prescription |
| `particulars` | String | Order details |
| `payment