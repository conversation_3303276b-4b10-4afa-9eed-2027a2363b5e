{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const db = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') {\n  globalForPrisma.prisma = db;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAE5D,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/test1/dashboard/app/api/prescriptions/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\nimport { db } from \"../../../lib/db\";\nimport { cookies } from \"next/headers\";\nimport { jwtVerify } from \"jose\";\n\n// JWT Secret\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.NEXTAUTH_SECRET || \"development-secret\"\n);\n\n// Helper function to get user from token\nasync function getUserFromToken() {\n  try {\n    const cookieList = await cookies();\n    const sessionToken = cookieList.get('auth-token')?.value;\n\n    if (!sessionToken) {\n      return null;\n    }\n\n    const { payload } = await jwtVerify(sessionToken, JWT_SECRET);\n    return payload.user as { id: string; email: string; name: string };\n  } catch (error) {\n    return null;\n  }\n}\n\nexport async function GET() {\n  const user = await getUserFromToken();\n  if (!user) {\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n\n  const prescriptions = await db.prescription.findMany({\n    where: { userId: user.id },\n    orderBy: { createdAt: \"desc\" },\n    include: {\n      orders: true\n    }\n  });\n\n  return NextResponse.json(prescriptions);\n}\n\nexport async function POST(request: Request) {\n  const user = await getUserFromToken();\n  if (!user) {\n    return NextResponse.json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n\n  const { prescriptionImg, description } = await request.json();\n\n  if (!prescriptionImg) {\n    return NextResponse.json({ error: \"Prescription image is required\" }, { status: 400 });\n  }\n\n  const prescription = await db.prescription.create({\n    data: {\n      userId: user.id,\n      prescriptionImg,\n      description,\n      status: \"submitted\"\n    },\n  });\n\n  return NextResponse.json(prescription);\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEA,aAAa;AACb,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,eAAe,IAAI;AAGjC,yCAAyC;AACzC,eAAe;IACb,IAAI;QACF,MAAM,aAAa,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC/B,MAAM,eAAe,WAAW,GAAG,CAAC,eAAe;QAEnD,IAAI,CAAC,cAAc;YACjB,OAAO;QACT;QAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAClD,OAAO,QAAQ,IAAI;IACrB,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,MAAM,gBAAgB,MAAM,2GAAA,CAAA,KAAE,CAAC,YAAY,CAAC,QAAQ,CAAC;QACnD,OAAO;YAAE,QAAQ,KAAK,EAAE;QAAC;QACzB,SAAS;YAAE,WAAW;QAAO;QAC7B,SAAS;YACP,QAAQ;QACV;IACF;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAEO,eAAe,KAAK,OAAgB;IACzC,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;IAE3D,IAAI,CAAC,iBAAiB;QACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiC,GAAG;YAAE,QAAQ;QAAI;IACtF;IAEA,MAAM,eAAe,MAAM,2GAAA,CAAA,KAAE,CAAC,YAAY,CAAC,MAAM,CAAC;QAChD,MAAM;YACJ,QAAQ,KAAK,EAAE;YACf;YACA;YACA,QAAQ;QACV;IACF;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B", "debugId": null}}]}