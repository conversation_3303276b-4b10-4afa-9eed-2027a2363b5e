"use strict";var q=Object.defineProperty;var U=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var C=(e,n)=>{for(var t in n)q(e,t,{get:n[t],enumerable:!0})},$=(e,n,t,_)=>{if(n&&typeof n=="object"||typeof n=="function")for(let c of B(n))!N.call(e,c)&&c!==t&&q(e,c,{get:()=>n[c],enumerable:!(_=U(n,c))||_.enumerable});return e};var V=e=>$(q({},"__esModule",{value:!0}),e);var Dn={};C(Dn,{QueryEngine:()=>Y,__wbg_String_88810dfeb4021902:()=>Ne,__wbg_buffer_b7b08af79b0b0974:()=>$e,__wbg_call_1084a111329e68ce:()=>en,__wbg_call_89af060b4e1523f2:()=>dn,__wbg_crypto_58f13aa23ffcb166:()=>Je,__wbg_done_bfda7aa8f252b39f:()=>_n,__wbg_entries_7a0e06255456ebcd:()=>jn,__wbg_exec_a29a4ce5544bd3be:()=>Ue,__wbg_getRandomValues_504510b5564925af:()=>Pe,__wbg_getTime_91058879093a1589:()=>se,__wbg_get_224d16597dbbfd96:()=>un,__wbg_get_3baa728f9d58d3f6:()=>nn,__wbg_get_94990005bd6ca07c:()=>Be,__wbg_getwithrefkey_5e6d9547403deab8:()=>Re,__wbg_globalThis_86b222e13bdf32ed:()=>an,__wbg_global_e5a3fe56f8be9485:()=>bn,__wbg_has_4bfbc01db38743f7:()=>ce,__wbg_instanceof_ArrayBuffer_61dfc3198373c902:()=>Sn,__wbg_instanceof_Promise_ae8c7ffdec83f2ae:()=>we,__wbg_instanceof_Uint8Array_247a91427532499e:()=>In,__wbg_isArray_8364a5371e9737d8:()=>ln,__wbg_isSafeInteger_7f1ed56200d90674:()=>wn,__wbg_iterator_888179a48810a9fe:()=>pe,__wbg_keys_7840ae453e408eab:()=>ge,__wbg_length_8339fcf5d8ecd12e:()=>yn,__wbg_length_ae22078168b726f5:()=>le,__wbg_msCrypto_abcb1295e768d1f2:()=>Xe,__wbg_new0_65387337a95cf44d:()=>ue,__wbg_new_13847c66f41dda63:()=>Me,__wbg_new_525245e2b9901204:()=>ke,__wbg_new_8608a2b51a5f6737:()=>qe,__wbg_new_a220cf903aa02ca2:()=>Ae,__wbg_new_b85e72ed1bfd57f9:()=>ee,__wbg_new_ea1883e1e5e86686:()=>ze,__wbg_newnoargs_76313bd6ff35d0f2:()=>gn,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9:()=>Ve,__wbg_newwithlength_ec548f448387c968:()=>Ye,__wbg_next_de3e9db4440638b2:()=>cn,__wbg_next_f9cb570345655b9a:()=>rn,__wbg_node_523d7bd03ef69fba:()=>Ge,__wbg_now_28a6b413aca4a96a:()=>hn,__wbg_now_8ed1a4454e40ecd1:()=>ae,__wbg_now_b7a162010a9e75b4:()=>be,__wbg_parse_52202f117ec9ecfa:()=>ie,__wbg_process_5b786e71d465a513:()=>Qe,__wbg_push_37c89022f34c01ca:()=>De,__wbg_randomFillSync_a0d98aa11c81fe89:()=>We,__wbg_require_2784e593a4674877:()=>Ke,__wbg_resolve_570458cb99d56a43:()=>Fn,__wbg_self_3093d5d1f7bcb682:()=>sn,__wbg_setTimeout_631fe61f31fa2fad:()=>ne,__wbg_set_49185437f0ab06f8:()=>Ee,__wbg_set_673dda6c73d19609:()=>Oe,__wbg_set_841ac57cff3d672b:()=>Fe,__wbg_set_d1e79e2388520f18:()=>mn,__wbg_set_eacc7d73fefaafdf:()=>pn,__wbg_set_wasm:()=>z,__wbg_stringify_bbf45426c92a6bf5:()=>xn,__wbg_subarray_7c2e3576afe181d1:()=>Le,__wbg_then_876bb3c633745cc6:()=>vn,__wbg_then_95e6edc0f89b73b1:()=>En,__wbg_valueOf_c759749a331da0c0:()=>tn,__wbg_value_6d39332ab4788d86:()=>on,__wbg_versions_c2ab80650590b6a2:()=>He,__wbg_window_3bcfc4d31bc012f8:()=>fn,__wbindgen_bigint_from_i64:()=>he,__wbindgen_bigint_from_u64:()=>Ie,__wbindgen_bigint_get_as_i64:()=>On,__wbindgen_boolean_get:()=>xe,__wbindgen_cb_drop:()=>kn,__wbindgen_closure_wrapper7176:()=>Rn,__wbindgen_debug_string:()=>qn,__wbindgen_error_new:()=>re,__wbindgen_in:()=>Te,__wbindgen_is_bigint:()=>me,__wbindgen_is_function:()=>Ze,__wbindgen_is_object:()=>de,__wbindgen_is_string:()=>ve,__wbindgen_is_undefined:()=>oe,__wbindgen_jsval_eq:()=>Se,__wbindgen_jsval_loose_eq:()=>Tn,__wbindgen_memory:()=>Ce,__wbindgen_number_get:()=>ye,__wbindgen_number_new:()=>je,__wbindgen_object_clone_ref:()=>_e,__wbindgen_object_drop_ref:()=>fe,__wbindgen_string_get:()=>Z,__wbindgen_string_new:()=>te,__wbindgen_throw:()=>An,debug_panic:()=>K,getBuildTimeInfo:()=>G});module.exports=V(Dn);var S=()=>{};S.prototype=S;let o;function z(e){o=e}const p=new Array(128).fill(void 0);p.push(void 0,null,!0,!1);function r(e){return p[e]}let a=0,j=null;function A(){return(j===null||j.byteLength===0)&&(j=new Uint8Array(o.memory.buffer)),j}const L=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let O=new L("utf-8");const P=typeof O.encodeInto=="function"?function(e,n){return O.encodeInto(e,n)}:function(e,n){const t=O.encode(e);return n.set(t),{read:e.length,written:t.length}};function b(e,n,t){if(t===void 0){const s=O.encode(e),w=n(s.length,1)>>>0;return A().subarray(w,w+s.length).set(s),a=s.length,w}let _=e.length,c=n(_,1)>>>0;const f=A();let u=0;for(;u<_;u++){const s=e.charCodeAt(u);if(s>127)break;f[c+u]=s}if(u!==_){u!==0&&(e=e.slice(u)),c=t(c,_,_=u+e.length*3,1)>>>0;const s=A().subarray(c+u,c+_),w=P(e,s);u+=w.written,c=t(c,_,u,1)>>>0}return a=u,c}function x(e){return e==null}let T=null;function d(){return(T===null||T.buffer.detached===!0||T.buffer.detached===void 0&&T.buffer!==o.memory.buffer)&&(T=new DataView(o.memory.buffer)),T}const W=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let E=new W("utf-8",{ignoreBOM:!0,fatal:!0});E.decode();function m(e,n){return e=e>>>0,E.decode(A().subarray(e,e+n))}let I=p.length;function i(e){I===p.length&&p.push(p.length+1);const n=I;return I=p[n],p[n]=e,n}function J(e){e<132||(p[e]=I,I=e)}function g(e){const n=r(e);return J(e),n}function k(e){const n=typeof e;if(n=="number"||n=="boolean"||e==null)return`${e}`;if(n=="string")return`"${e}"`;if(n=="symbol"){const c=e.description;return c==null?"Symbol":`Symbol(${c})`}if(n=="function"){const c=e.name;return typeof c=="string"&&c.length>0?`Function(${c})`:"Function"}if(Array.isArray(e)){const c=e.length;let f="[";c>0&&(f+=k(e[0]));for(let u=1;u<c;u++)f+=", "+k(e[u]);return f+="]",f}const t=/\[object ([^\]]+)\]/.exec(toString.call(e));let _;if(t.length>1)_=t[1];else return toString.call(e);if(_=="Object")try{return"Object("+JSON.stringify(e)+")"}catch{return"Object"}return e instanceof Error?`${e.name}: ${e.message}
${e.stack}`:_}const v=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>{o.__wbindgen_export_2.get(e.dtor)(e.a,e.b)});function Q(e,n,t,_){const c={a:e,b:n,cnt:1,dtor:t},f=(...u)=>{c.cnt++;const s=c.a;c.a=0;try{return _(s,c.b,...u)}finally{--c.cnt===0?(o.__wbindgen_export_2.get(c.dtor)(s,c.b),v.unregister(c)):c.a=s}};return f.original=c,v.register(f,c,c),f}function H(e,n,t){o._dyn_core__ops__function__FnMut__A____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h9f2fcea3357d311c(e,n,i(t))}function G(){const e=o.getBuildTimeInfo();return g(e)}function K(e){try{const f=o.__wbindgen_add_to_stack_pointer(-16);var n=x(e)?0:b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),t=a;o.debug_panic(f,n,t);var _=d().getInt32(f+4*0,!0),c=d().getInt32(f+4*1,!0);if(c)throw g(_)}finally{o.__wbindgen_add_to_stack_pointer(16)}}function l(e,n){try{return e.apply(this,n)}catch(t){o.__wbindgen_exn_store(i(t))}}function X(e,n,t,_){o.wasm_bindgen__convert__closures__invoke2_mut__h157f32060ce6ea34(e,n,i(t),i(_))}const F=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>o.__wbg_queryengine_free(e>>>0,1));class Y{__destroy_into_raw(){const n=this.__wbg_ptr;return this.__wbg_ptr=0,F.unregister(this),n}free(){const n=this.__destroy_into_raw();o.__wbg_queryengine_free(n,0)}constructor(n,t,_){try{const s=o.__wbindgen_add_to_stack_pointer(-16);o.queryengine_new(s,i(n),i(t),i(_));var c=d().getInt32(s+4*0,!0),f=d().getInt32(s+4*1,!0),u=d().getInt32(s+4*2,!0);if(u)throw g(f);return this.__wbg_ptr=c>>>0,F.register(this,this.__wbg_ptr,this),this}finally{o.__wbindgen_add_to_stack_pointer(16)}}connect(n,t){const _=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a,f=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),u=a,s=o.queryengine_connect(this.__wbg_ptr,_,c,f,u);return g(s)}disconnect(n,t){const _=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a,f=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),u=a,s=o.queryengine_disconnect(this.__wbg_ptr,_,c,f,u);return g(s)}query(n,t,_,c){const f=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),u=a,s=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),w=a;var y=x(_)?0:b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),h=a;const R=b(c,o.__wbindgen_malloc,o.__wbindgen_realloc),D=a,M=o.queryengine_query(this.__wbg_ptr,f,u,s,w,y,h,R,D);return g(M)}startTransaction(n,t,_){const c=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a,u=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),s=a,w=b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),y=a,h=o.queryengine_startTransaction(this.__wbg_ptr,c,f,u,s,w,y);return g(h)}commitTransaction(n,t,_){const c=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a,u=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),s=a,w=b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),y=a,h=o.queryengine_commitTransaction(this.__wbg_ptr,c,f,u,s,w,y);return g(h)}rollbackTransaction(n,t,_){const c=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a,u=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),s=a,w=b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),y=a,h=o.queryengine_rollbackTransaction(this.__wbg_ptr,c,f,u,s,w,y);return g(h)}metrics(n){const t=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),_=a,c=o.queryengine_metrics(this.__wbg_ptr,t,_);return g(c)}trace(n){const t=b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),_=a,c=o.queryengine_trace(this.__wbg_ptr,t,_);return g(c)}}function Z(e,n){const t=r(n),_=typeof t=="string"?t:void 0;var c=x(_)?0:b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a;d().setInt32(e+4*1,f,!0),d().setInt32(e+4*0,c,!0)}function ee(e,n){try{var t={a:e,b:n},_=(f,u)=>{const s=t.a;t.a=0;try{return X(s,t.b,f,u)}finally{t.a=s}};const c=new Promise(_);return i(c)}finally{t.a=t.b=0}}function ne(e,n){return setTimeout(r(e),n>>>0)}function te(e,n){const t=m(e,n);return i(t)}function re(e,n){const t=new Error(m(e,n));return i(t)}function _e(e){const n=r(e);return i(n)}function oe(e){return r(e)===void 0}function ce(){return l(function(e,n){return Reflect.has(r(e),r(n))},arguments)}function ie(){return l(function(e,n){const t=JSON.parse(m(e,n));return i(t)},arguments)}function ue(){return i(new Date)}function se(e){return r(e).getTime()}function fe(e){g(e)}function ae(e){return r(e).now()}function be(){return Date.now()}function ge(e){const n=Object.keys(r(e));return i(n)}function le(e){return r(e).length}function de(e){const n=r(e);return typeof n=="object"&&n!==null}function we(e){let n;try{n=r(e)instanceof Promise}catch{n=!1}return n}function pe(){return i(Symbol.iterator)}function xe(e){const n=r(e);return typeof n=="boolean"?n?1:0:2}function me(e){return typeof r(e)=="bigint"}function ye(e,n){const t=r(n),_=typeof t=="number"?t:void 0;d().setFloat64(e+8*1,x(_)?0:_,!0),d().setInt32(e+4*0,!x(_),!0)}function he(e){return i(e)}function Te(e,n){return r(e)in r(n)}function Ie(e){const n=BigInt.asUintN(64,e);return i(n)}function Se(e,n){return r(e)===r(n)}function je(e){return i(e)}function Ae(){const e=new Array;return i(e)}function Oe(e,n,t){r(e)[n>>>0]=g(t)}function qe(){return i(new Map)}function ke(){const e=new Object;return i(e)}function Ee(e,n,t){const _=r(e).set(r(n),r(t));return i(_)}function ve(e){return typeof r(e)=="string"}function Fe(e,n,t){r(e)[g(n)]=g(t)}function Re(e,n){const t=r(e)[r(n)];return i(t)}function De(e,n){return r(e).push(r(n))}function Me(e,n,t,_){const c=new RegExp(m(e,n),m(t,_));return i(c)}function Ue(e,n,t){const _=r(e).exec(m(n,t));return x(_)?0:i(_)}function Be(){return l(function(e,n){const t=r(e)[g(n)];return i(t)},arguments)}function Ne(e,n){const t=String(r(n)),_=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a;d().setInt32(e+4*1,c,!0),d().setInt32(e+4*0,_,!0)}function Ce(){const e=o.memory;return i(e)}function $e(e){const n=r(e).buffer;return i(n)}function Ve(e,n,t){const _=new Uint8Array(r(e),n>>>0,t>>>0);return i(_)}function ze(e){const n=new Uint8Array(r(e));return i(n)}function Le(e,n,t){const _=r(e).subarray(n>>>0,t>>>0);return i(_)}function Pe(){return l(function(e,n){r(e).getRandomValues(r(n))},arguments)}function We(){return l(function(e,n){r(e).randomFillSync(g(n))},arguments)}function Je(e){const n=r(e).crypto;return i(n)}function Qe(e){const n=r(e).process;return i(n)}function He(e){const n=r(e).versions;return i(n)}function Ge(e){const n=r(e).node;return i(n)}function Ke(){return l(function(){const e=module.require;return i(e)},arguments)}function Xe(e){const n=r(e).msCrypto;return i(n)}function Ye(e){const n=new Uint8Array(e>>>0);return i(n)}function Ze(e){return typeof r(e)=="function"}function en(){return l(function(e,n){const t=r(e).call(r(n));return i(t)},arguments)}function nn(e,n){const t=r(e)[n>>>0];return i(t)}function tn(e){return r(e).valueOf()}function rn(){return l(function(e){const n=r(e).next();return i(n)},arguments)}function _n(e){return r(e).done}function on(e){const n=r(e).value;return i(n)}function cn(e){const n=r(e).next;return i(n)}function un(){return l(function(e,n){const t=Reflect.get(r(e),r(n));return i(t)},arguments)}function sn(){return l(function(){const e=self.self;return i(e)},arguments)}function fn(){return l(function(){const e=window.window;return i(e)},arguments)}function an(){return l(function(){const e=globalThis.globalThis;return i(e)},arguments)}function bn(){return l(function(){const e=global.global;return i(e)},arguments)}function gn(e,n){const t=new S(m(e,n));return i(t)}function ln(e){return Array.isArray(r(e))}function dn(){return l(function(e,n,t){const _=r(e).call(r(n),r(t));return i(_)},arguments)}function wn(e){return Number.isSafeInteger(r(e))}function pn(){return l(function(e,n,t){return Reflect.set(r(e),r(n),r(t))},arguments)}function xn(){return l(function(e){const n=JSON.stringify(r(e));return i(n)},arguments)}function mn(e,n,t){r(e).set(r(n),t>>>0)}function yn(e){return r(e).length}function hn(){return l(function(){return Date.now()},arguments)}function Tn(e,n){return r(e)==r(n)}function In(e){let n;try{n=r(e)instanceof Uint8Array}catch{n=!1}return n}function Sn(e){let n;try{n=r(e)instanceof ArrayBuffer}catch{n=!1}return n}function jn(e){const n=Object.entries(r(e));return i(n)}function An(e,n){throw new Error(m(e,n))}function On(e,n){const t=r(n),_=typeof t=="bigint"?t:void 0;d().setBigInt64(e+8*1,x(_)?BigInt(0):_,!0),d().setInt32(e+4*0,!x(_),!0)}function qn(e,n){const t=k(r(n)),_=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a;d().setInt32(e+4*1,c,!0),d().setInt32(e+4*0,_,!0)}function kn(e){const n=g(e).original;return n.cnt--==1?(n.a=0,!0):!1}function En(e,n){const t=r(e).then(r(n));return i(t)}function vn(e,n,t){const _=r(e).then(r(n),r(t));return i(_)}function Fn(e){const n=Promise.resolve(r(e));return i(n)}function Rn(e,n,t){const _=Q(e,n,546,H);return i(_)}0&&(module.exports={QueryEngine,__wbg_String_88810dfeb4021902,__wbg_buffer_b7b08af79b0b0974,__wbg_call_1084a111329e68ce,__wbg_call_89af060b4e1523f2,__wbg_crypto_58f13aa23ffcb166,__wbg_done_bfda7aa8f252b39f,__wbg_entries_7a0e06255456ebcd,__wbg_exec_a29a4ce5544bd3be,__wbg_getRandomValues_504510b5564925af,__wbg_getTime_91058879093a1589,__wbg_get_224d16597dbbfd96,__wbg_get_3baa728f9d58d3f6,__wbg_get_94990005bd6ca07c,__wbg_getwithrefkey_5e6d9547403deab8,__wbg_globalThis_86b222e13bdf32ed,__wbg_global_e5a3fe56f8be9485,__wbg_has_4bfbc01db38743f7,__wbg_instanceof_ArrayBuffer_61dfc3198373c902,__wbg_instanceof_Promise_ae8c7ffdec83f2ae,__wbg_instanceof_Uint8Array_247a91427532499e,__wbg_isArray_8364a5371e9737d8,__wbg_isSafeInteger_7f1ed56200d90674,__wbg_iterator_888179a48810a9fe,__wbg_keys_7840ae453e408eab,__wbg_length_8339fcf5d8ecd12e,__wbg_length_ae22078168b726f5,__wbg_msCrypto_abcb1295e768d1f2,__wbg_new0_65387337a95cf44d,__wbg_new_13847c66f41dda63,__wbg_new_525245e2b9901204,__wbg_new_8608a2b51a5f6737,__wbg_new_a220cf903aa02ca2,__wbg_new_b85e72ed1bfd57f9,__wbg_new_ea1883e1e5e86686,__wbg_newnoargs_76313bd6ff35d0f2,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9,__wbg_newwithlength_ec548f448387c968,__wbg_next_de3e9db4440638b2,__wbg_next_f9cb570345655b9a,__wbg_node_523d7bd03ef69fba,__wbg_now_28a6b413aca4a96a,__wbg_now_8ed1a4454e40ecd1,__wbg_now_b7a162010a9e75b4,__wbg_parse_52202f117ec9ecfa,__wbg_process_5b786e71d465a513,__wbg_push_37c89022f34c01ca,__wbg_randomFillSync_a0d98aa11c81fe89,__wbg_require_2784e593a4674877,__wbg_resolve_570458cb99d56a43,__wbg_self_3093d5d1f7bcb682,__wbg_setTimeout_631fe61f31fa2fad,__wbg_set_49185437f0ab06f8,__wbg_set_673dda6c73d19609,__wbg_set_841ac57cff3d672b,__wbg_set_d1e79e2388520f18,__wbg_set_eacc7d73fefaafdf,__wbg_set_wasm,__wbg_stringify_bbf45426c92a6bf5,__wbg_subarray_7c2e3576afe181d1,__wbg_then_876bb3c633745cc6,__wbg_then_95e6edc0f89b73b1,__wbg_valueOf_c759749a331da0c0,__wbg_value_6d39332ab4788d86,__wbg_versions_c2ab80650590b6a2,__wbg_window_3bcfc4d31bc012f8,__wbindgen_bigint_from_i64,__wbindgen_bigint_from_u64,__wbindgen_bigint_get_as_i64,__wbindgen_boolean_get,__wbindgen_cb_drop,__wbindgen_closure_wrapper7176,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_is_bigint,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_eq,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_number_new,__wbindgen_object_clone_ref,__wbindgen_object_drop_ref,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw,debug_panic,getBuildTimeInfo});
