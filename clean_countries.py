import mysql.connector
import re
from collections import defaultdict

def create_backup_table(conn):
    """Create a backup of the current users table"""
    cursor = conn.cursor()
    try:
        # Drop backup table if it exists
        cursor.execute("DROP TABLE IF EXISTS users_country_backup")
        
        # Create backup table with the same structure
        cursor.execute("""
            CREATE TABLE users_country_backup AS
            SELECT id, country, email, created_at 
            FROM users
        """)
        conn.commit()
        print("✓ Created backup of country data in 'users_country_backup' table")
        return True
    except mysql.connector.Error as err:
        print(f"Error creating backup: {err}")
        conn.rollback()
        return False

def get_country_mapping():
    """Return a comprehensive mapping of country name variations to standard English names"""
    return {
        # North America
        'united states': 'United States',
        'usa': 'United States',
        'us': 'United States',
        'u.s.': 'United States',
        'u.s.a.': 'United States',
        'united states of america': 'United States',
        'estados unidos': 'United States',  # Spanish
        'états-unis': 'United States',  # French
        'vereinigte staaten': 'United States',  # German
        'stati uniti': 'United States',  # Italian
        'estados unidos da américa': 'United States',  # Portuguese
        'сша': 'United States',  # Russian (abbreviation)
        'соединенные штаты': 'United States',  # Russian
        '美国': 'United States',  # Chinese
        'アメリカ': 'United States',  # Japanese
        '미국': 'United States',  # Korean
        'kanada': 'Canada',  # Various languages
        'canadá': 'Canada',
        'le canada': 'Canada',
        
        # Europe
        'united kingdom': 'United Kingdom',
        'uk': 'United Kingdom',
        'u.k.': 'United Kingdom',
        'great britain': 'United Kingdom',
        'britain': 'United Kingdom',
        'england': 'United Kingdom',
        'scotland': 'United Kingdom',
        'wales': 'United Kingdom',
        'northern ireland': 'United Kingdom',
        'gb': 'United Kingdom',
        'g.b.': 'United Kingdom',
        'royaume-uni': 'United Kingdom',  # French
        'reino unido': 'United Kingdom',  # Spanish
        'regno unito': 'United Kingdom',  # Italian
        'vereinigtes königreich': 'United Kingdom',  # German
        'inggris': 'United Kingdom',  # Indonesian
        'inggris raya': 'United Kingdom',  # Malay
        
        # Western Europe
        'deutschland': 'Germany',
        'germany': 'Germany',
        'alemania': 'Germany',  # Spanish
        'allemagne': 'Germany',  # French
        'tedesco': 'Germany',  # Italian
        'alemanha': 'Germany',  # Portuguese
        'германия': 'Germany',  # Russian
        'ドイツ': 'Germany',  # Japanese
        '독일': 'Germany',  # Korean
        'france': 'France',
        'frankreich': 'France',  # German
        'francia': 'France',  # Italian/Spanish
        'frankrijk': 'France',  # Dutch
        'франция': 'France',  # Russian
        '프랑스': 'France',  # Korean
        'espana': 'Spain',
        'españa': 'Spain',
        'spanien': 'Spain',  # German/Swedish
        'espagne': 'Spain',  # French
        'spagna': 'Spain',  # Italian
        'espanha': 'Spain',  # Portuguese
        'испания': 'Spain',  # Russian
        '스페인': 'Spain',  # Korean
        'italy': 'Italy',
        'italia': 'Italy',  # Italian/Spanish
        'italie': 'Italy',  # French
        'italien': 'Italy',  # German/Swedish
        'италия': 'Italy',  # Russian
        '이탈리아': 'Italy',  # Korean
        'nederland': 'Netherlands',
        'holland': 'Netherlands',
        'pays-bas': 'Netherlands',  # French
        'paesi bassi': 'Netherlands',  # Italian
        'niederlande': 'Netherlands',  # German
        'holanda': 'Netherlands',  # Spanish/Portuguese
        '네덜란드': 'Netherlands',  # Korean
        'belgique': 'Belgium',  # French
        'belgien': 'Belgium',  # German
        'belgica': 'Belgium',  # Spanish/Portuguese
        'belgio': 'Belgium',  # Italian
        'belgicko': 'Belgium',  # Czech
        'ベルギー': 'Belgium',  # Japanese
        '벨기에': 'Belgium',  # Korean
        'schweiz': 'Switzerland',  # German
        'suisse': 'Switzerland',  # French
        'svizzera': 'Switzerland',  # Italian
        'svizra': 'Switzerland',  # Romansh
        'швейцария': 'Switzerland',  # Russian
        '스위스': 'Switzerland',  # Korean
        'österreich': 'Austria',  # German
        'austria': 'Austria',
        'autriche': 'Austria',  # French
        'austrija': 'Austria',  # Croatian
        'オーストリア': 'Austria',  # Japanese
        '오스트리아': 'Austria',  # Korean
        
        # Nordic Countries
        'sverige': 'Sweden',  # Swedish
        'schweden': 'Sweden',  # German
        'suède': 'Sweden',  # French
        'suecia': 'Sweden',  # Spanish
        'svezia': 'Sweden',  # Italian
        'norge': 'Norway',  # Norwegian
        'noreg': 'Norway',  # Nynorsk
        'norvège': 'Norway',  # French
        'norwegia': 'Norway',  # Polish
        'danmark': 'Denmark',  # Danish
        'dänemark': 'Denmark',  # German
        'danemark': 'Denmark',  # French
        'dinamarca': 'Denmark',  # Spanish/Portuguese
        'finland': 'Finland',
        'suomi': 'Finland',  # Finnish
        'finnland': 'Finland',  # German
        'finlande': 'Finland',  # French
        'finlandia': 'Finland',  # Italian/Spanish
        
        # Southern Europe
        'portugal': 'Portugal',  # Same in many languages
        'portugália': 'Portugal',  # Hungarian
        'portogallo': 'Portugal',  # Italian
        'portugalio': 'Portugal',  # Esperanto
        'grecia': 'Greece',  # Italian/Spanish/Portuguese
        'greece': 'Greece',
        'ελλάδα': 'Greece',  # Greek
        'ελλαδα': 'Greece',  # Greek without accents
        'grèce': 'Greece',  # French
        'griechenland': 'Greece',  # German
        'grekland': 'Greece',  # Swedish
        'grčka': 'Greece',  # Croatian
        'grécia': 'Greece',  # Hungarian
        'ギリシャ': 'Greece',  # Japanese
        '그리스': 'Greece',  # Korean
        
        # Eastern Europe
        'rossiya': 'Russia',  # Russian (transliterated)
        'russia': 'Russia',
        'russie': 'Russia',  # French
        'rusia': 'Russia',  # Spanish/Italian
        'rússia': 'Russia',  # Portuguese
        '러시아': 'Russia',  # Korean
        'polska': 'Poland',  # Polish
        'polen': 'Poland',  # German/Dutch/Swedish
        'pologne': 'Poland',  # French
        'polonia': 'Poland',  # Italian/Spanish
        'polónia': 'Poland',  # Portuguese
        'ukrayina': 'Ukraine',  # Ukrainian
        'ukraine': 'Ukraine',
        'ucraina': 'Ukraine',  # Italian/Spanish
        'ukrayna': 'Ukraine',  # Turkish
        'ukrajina': 'Ukraine',  # Czech/Slovak
        
        # Asia
        'zhongguo': 'China',  # Chinese (Pinyin)
        'china': 'China',
        'chine': 'China',  # French
        'cina': 'China',  # Italian/Indonesian
        'chińsk': 'China',  # Polish
        '中国': 'China',  # Chinese (Simplified)
        '중국': 'China',  # Korean
        'nippon': 'Japan',  # Japanese (Nihon/Nippon)
        'japan': 'Japan',
        'japon': 'Japan',  # French/Spanish
        'giappone': 'Japan',  # Italian
        'japão': 'Japan',  # Portuguese
        '日本': 'Japan',  # Japanese
        '일본': 'Japan',  # Korean
        'daehan minguk': 'South Korea',  # Korean (transliterated)
        'south korea': 'South Korea',
        'korea': 'South Korea',
        'corée du sud': 'South Korea',  # French
        'südkorea': 'South Korea',  # German
        'corea del sud': 'South Korea',  # Italian
        'coreia do sul': 'South Korea',  # Portuguese
        '대한민국': 'South Korea',  # Korean
        '한국': 'South Korea',  # Korea (abbreviation)
        
        # Add more countries and variations as needed...
        'australia': 'Australia',
        'australie': 'Australia',  # French
        'australien': 'Australia',  # German
        'australië': 'Australia',  # Dutch
        'オーストラリア': 'Australia',  # Japanese
        '호주': 'Australia',  # Korean
        'new zealand': 'New Zealand',
        'nueva zelanda': 'New Zealand',  # Spanish
        'nouvelle-zélande': 'New Zealand',  # French
        'neuseeland': 'New Zealand',  # German
        'nuova zelanda': 'New Zealand',  # Italian
    }

def clean_country_name(country):
    """Clean and standardize a country name"""
    if not country or country.strip() == '':
        return None
    
    # Keep original for reference
    original = country.strip()
    
    # Convert to lowercase and trim
    cleaned = original.lower().strip()
    
    # Handle empty or invalid entries
    if cleaned in ['-', '--', '---', 'n/a', 'na', 'none', 'null', 'undefined', 'unknown', 'unkown']:
        return None
    
    # Handle common abbreviations and special cases
    special_cases = {
        # Greek variations
        'ελλάδα': 'Greece',
        'ελλαδα': 'Greece',
        'ελλας': 'Greece',
        'ελλ': 'Greece',
        'ελλάδ': 'Greece',
        
        # Belgium variations
        'belgique': 'Belgium',
        'belgie': 'Belgium',
        'belgien': 'Belgium',
        'belgicko': 'Belgium',
        'belgica': 'Belgium',
        'belgio': 'Belgium',
        'belg': 'Belgium',
        'belgi': 'Belgium',
        
        # Common abbreviations
        'uk': 'United Kingdom',
        'u.k.': 'United Kingdom',
        'u.k': 'United Kingdom',
        'usa': 'United States',
        'u.s.': 'United States',
        'u.s': 'United States',
        'us': 'United States',
        'u.s.a.': 'United States',
        'u.s.a': 'United States',
        
        # Country codes
        'gb': 'United Kingdom',
        'gbr': 'United Kingdom',
        'us': 'United States',
        'usa': 'United States',
        'de': 'Germany',
        'fr': 'France',
        'it': 'Italy',
        'es': 'Spain',
        'nl': 'Netherlands',
        'be': 'Belgium',
        'gr': 'Greece',
        'se': 'Sweden',
        'no': 'Norway',
        'dk': 'Denmark',
        'fi': 'Finland',
        'ie': 'Ireland',
        'ch': 'Switzerland',
        'at': 'Austria',
        'pt': 'Portugal',
        'pl': 'Poland',
        'cz': 'Czech Republic',
        'sk': 'Slovakia',
        'hu': 'Hungary',
        'ro': 'Romania',
        'bg': 'Bulgaria',
        'hr': 'Croatia',
        'si': 'Slovenia',
        'rs': 'Serbia',
        'me': 'Montenegro',
        'mk': 'North Macedonia',
        'al': 'Albania',
        'ba': 'Bosnia and Herzegovina',
        'md': 'Moldova',
        'ua': 'Ukraine',
        'by': 'Belarus',
        'ru': 'Russia',
        'kz': 'Kazakhstan',
        'tr': 'Turkey',
        'il': 'Israel',
        'cy': 'Cyprus',
        'mt': 'Malta',
        'is': 'Iceland',
        'li': 'Liechtenstein',
        'lu': 'Luxembourg',
        'mc': 'Monaco',
        'sm': 'San Marino',
        'va': 'Vatican City',
        'ad': 'Andorra',
        'us': 'United States',
        'ca': 'Canada',
        'mx': 'Mexico',
        'au': 'Australia',
        'nz': 'New Zealand',
        'za': 'South Africa',
        'in': 'India',
        'cn': 'China',
        'jp': 'Japan',
        'kr': 'South Korea',
        'sg': 'Singapore',
        'my': 'Malaysia',
        'th': 'Thailand',
        'vn': 'Vietnam',
        'id': 'Indonesia',
        'ph': 'Philippines',
        'br': 'Brazil',
        'ar': 'Argentina',
        'cl': 'Chile',
        'co': 'Colombia',
        'pe': 'Peru',
        've': 'Venezuela',
        'ng': 'Nigeria',
        'eg': 'Egypt',
        'sa': 'Saudi Arabia',
        'ae': 'United Arab Emirates',
        'qa': 'Qatar',
        'kw': 'Kuwait',
        'bh': 'Bahrain',
        'om': 'Oman',
        'jo': 'Jordan',
        'lb': 'Lebanon',
        'sy': 'Syria',
        'iq': 'Iraq',
        'ir': 'Iran',
        'pk': 'Pakistan',
        'bd': 'Bangladesh',
        'lk': 'Sri Lanka',
        'np': 'Nepal',
        'bt': 'Bhutan',
        'mv': 'Maldives',
        'mm': 'Myanmar',
        'la': 'Laos',
        'kh': 'Cambodia',
        'bn': 'Brunei',
        'tl': 'Timor-Leste',
        'pg': 'Papua New Guinea',
        'fj': 'Fiji',
        'sb': 'Solomon Islands',
        'vu': 'Vanuatu',
        'ws': 'Samoa',
        'to': 'Tonga',
        'ki': 'Kiribati',
        'mh': 'Marshall Islands',
        'nr': 'Nauru',
        'tv': 'Tuvalu',
        'pw': 'Palau',
        'fm': 'Micronesia',
        'ws': 'Samoa',
        'to': 'Tonga',
        'ki': 'Kiribati',
        'mh': 'Marshall Islands',
        'nr': 'Nauru',
        'tv': 'Tuvalu',
        'pw': 'Palau',
        'fm': 'Micronesia'
    }
    
    # Check special cases first
    if cleaned in special_cases:
        return special_cases[cleaned]
    
    # Handle patterns
    if 'kingdom' in cleaned and ('united' in cleaned or 'u.k' in cleaned or 'uk' in cleaned):
        return 'United Kingdom'
    if 'states' in cleaned and ('united' in cleaned or 'u.s' in cleaned or 'usa' in cleaned):
        return 'United States'
    if 'russia' in cleaned or 'россия' in cleaned or 'rossiya' in cleaned:
        return 'Russia'
    if 'germany' in cleaned or 'deutschland' in cleaned or 'germania' in cleaned:
        return 'Germany'
    if 'france' in cleaned or 'frankreich' in cleaned or 'francia' in cleaned:
        return 'France'
    if 'italy' in cleaned or 'italia' in cleaned or 'italie' in cleaned:
        return 'Italy'
    if 'spain' in cleaned or 'espana' in cleaned or 'españa' in cleaned:
        return 'Spain'
    if 'netherlands' in cleaned or 'nederland' in cleaned or 'holland' in cleaned:
        return 'Netherlands'
    if 'switzerland' in cleaned or 'schweiz' in cleaned or 'suisse' in cleaned or 'svizzera' in cleaned:
        return 'Switzerland'
    if 'austria' in cleaned or 'österreich' in cleaned or 'osterreich' in cleaned:
        return 'Austria'
    if 'sweden' in cleaned or 'sverige' in cleaned or 'sverige' in cleaned:
        return 'Sweden'
    if 'norway' in cleaned or 'norge' in cleaned or 'noreg' in cleaned:
        return 'Norway'
    if 'denmark' in cleaned or 'danmark' in cleaned or 'danmark' in cleaned:
        return 'Denmark'
    if 'finland' in cleaned or 'suomi' in cleaned or 'finland' in cleaned:
        return 'Finland'
    if 'portugal' in cleaned or 'portugal' in cleaned or 'portogallo' in cleaned:
        return 'Portugal'
    if 'greece' in cleaned or 'ελλάδα' in cleaned or 'ελλαδα' in cleaned:
        return 'Greece'
    if 'poland' in cleaned or 'polska' in cleaned or 'polen' in cleaned:
        return 'Poland'
    if 'ukraine' in cleaned or 'ukrayina' in cleaned or 'україна' in cleaned:
        return 'Ukraine'
    if 'china' in cleaned or '中国' in cleaned or 'zhongguo' in cleaned:
        return 'China'
    if 'japan' in cleaned or '日本' in cleaned or 'nippon' in cleaned:
        return 'Japan'
    if 'south korea' in cleaned or '대한민국' in cleaned or 'hanguk' in cleaned:
        return 'South Korea'
    if 'australia' in cleaned or 'australia' in cleaned or 'australie' in cleaned:
        return 'Australia'
    if 'new zealand' in cleaned or 'new zealand' in cleaned or 'aotearoa' in cleaned:
        return 'New Zealand'
    
    # Remove any non-alphabetic characters except spaces and hyphens
    cleaned = re.sub(r'[^a-z\s-]', '', cleaned)
    
    # Get country mapping
    country_map = get_country_mapping()
    
    # Try direct match first
    if cleaned in country_map:
        return country_map[cleaned]
    
    # Try partial matches for common patterns
    for variant, standard in country_map.items():
        if variant in cleaned or cleaned in variant:
            return standard
    
    # Try removing common suffixes/prefixes and try again
    suffixes = ['republic of', 'kingdom of', 'state of', 'federal republic of', 
               'united states of', 'united kingdom of']
    
    cleaned_variant = cleaned
    for suffix in suffixes:
        cleaned_variant = cleaned_variant.replace(suffix, '').strip()
    
    if cleaned_variant in country_map:
        return country_map[cleaned_variant]
    
    # If we still don't have a match, try to find the most similar country name
    # This is a simple implementation - for production, consider using a library like fuzzywuzzy
    cleaned_variant = cleaned_variant.replace('the', '').strip()
    if cleaned_variant in country_map:
        return country_map[cleaned_variant]
    
    # As a last resort, return the original with proper title case
    return original.title()

def update_country_data(conn):
    """Update the country data in the users table"""
    cursor = conn.cursor(dictionary=True)
    
    try:
        # Get all unique country values
        cursor.execute("SELECT DISTINCT country FROM users WHERE country IS NOT NULL")
        countries = [row['country'] for row in cursor.fetchall()]
        
        # Track changes
        changes = defaultdict(int)
        
        # Process each country
        for country in countries:
            if country is None:
                continue
                
            cleaned = clean_country_name(country)
            
            # Only update if the cleaned version is different
            if cleaned and cleaned != country:
                update_query = """
                    UPDATE users 
                    SET country = %s 
                    WHERE country = %s
                """
                cursor.execute(update_query, (cleaned, country))
                changes[f"{country} → {cleaned}"] = cursor.rowcount
        
        conn.commit()
        
        # Print summary of changes
        print("\nCountry name standardization summary:")
        print("-" * 60)
        for change, count in changes.items():
            print(f"Updated {count} records: {change}")
            
        # Show remaining unique countries
        cursor.execute("""
            SELECT 
                country, 
                COUNT(*) as user_count 
            FROM users 
            WHERE country IS NOT NULL 
            GROUP BY country 
            ORDER BY user_count DESC
            LIMIT 20
        """)
        
        print("\nTop countries after standardization:")
        print("-" * 60)
        for row in cursor.fetchall():
            print(f"{row['country']}: {row['user_count']} users")
        
        # Count unique countries
        cursor.execute("SELECT COUNT(DISTINCT country) as unique_countries FROM users")
        count = cursor.fetchone()['unique_countries']
        print(f"\nTotal unique countries after cleaning: {count}")
        
    except mysql.connector.Error as err:
        print(f"Error updating country data: {err}")
        conn.rollback()
    finally:
        cursor.close()

def main():
    try:
        # Connect to the database
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="nplabs dev"
        )
        
        print("Starting country data standardization...")
        
        # Create backup first
        if not create_backup_table(conn):
            print("Failed to create backup. Exiting.")
            return
        
        # Update the country data
        update_country_data(conn)
        
        print("\n✓ Country data standardization complete!")
        print("A backup of the original data is available in the 'users_country_backup' table.")
        
    except mysql.connector.Error as err:
        print(f"Database connection error: {err}")
    finally:
        if conn.is_connected():
            conn.close()

if __name__ == "__main__":
    main()
