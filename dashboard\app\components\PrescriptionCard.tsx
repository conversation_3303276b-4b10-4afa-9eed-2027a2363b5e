"use client";
import { motion } from "framer-motion";
import { 
  ClockI<PERSON>, 
  EyeIcon, 
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from "@heroicons/react/24/outline";
import { Card, CardContent } from "./ui/Card";
import { cn, formatRelativeTime, getStatusColor, getStatusProgress } from "@/lib/utils";

interface Prescription {
  id: string;
  prescriptionImg: string;
  description?: string;
  status: string;
  createdAt: string;
}

interface PrescriptionCardProps {
  prescription: Prescription;
  onClick: () => void;
  index: number;
}

const statusIcons = {
  submitted: ClockIcon,
  review: ExclamationTriangleIcon,
  approved: CheckCircleIcon,
  compounding: ClockIcon,
  quality_check: CheckCircleIcon,
  packaging: ClockIcon,
  shipped: ClockIcon,
  delivered: CheckCircleIcon,
};

export default function PrescriptionCard({ prescription, onClick, index }: PrescriptionCardProps) {
  const StatusIcon = statusIcons[prescription.status as keyof typeof statusIcons] || ClockIcon;
  const progress = getStatusProgress(prescription.status);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ y: -4 }}
      className="group"
    >
      <Card 
        className="overflow-hidden cursor-pointer bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300"
        onClick={onClick}
      >
        <div className="relative">
          {/* Progress bar */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700">
            <motion.div
              className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 1, delay: index * 0.1 + 0.3 }}
            />
          </div>

          {/* Image */}
          <div className="relative h-48 overflow-hidden">
            <img
              src={prescription.prescriptionImg}
              alt={prescription.description || "Prescription"}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            
            {/* Status badge */}
            <div className="absolute top-3 right-3">
              <div className={cn(
                "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border backdrop-blur-sm",
                getStatusColor(prescription.status)
              )}>
                <StatusIcon className="w-3 h-3" />
                <span className="capitalize">
                  {prescription.status.replace('_', ' ')}
                </span>
              </div>
            </div>

            {/* View overlay */}
            <motion.div
              className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              initial={false}
            >
              <motion.div
                className="flex items-center space-x-2 text-white font-medium"
                initial={{ scale: 0.8 }}
                whileHover={{ scale: 1 }}
              >
                <EyeIcon className="w-5 h-5" />
                <span>View Details</span>
              </motion.div>
            </motion.div>
          </div>

          <CardContent className="p-4">
            {/* Description */}
            {prescription.description && (
              <h3 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
                {prescription.description}
              </h3>
            )}

            {/* Meta info */}
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <CalendarIcon className="w-4 h-4" />
                <span>{formatRelativeTime(prescription.createdAt)}</span>
              </div>
              
              <motion.div
                className="text-blue-600 dark:text-blue-400 font-medium"
                whileHover={{ scale: 1.05 }}
              >
                View →
              </motion.div>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
}
