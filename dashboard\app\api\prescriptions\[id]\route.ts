import { NextResponse } from "next/server";
import { db } from "../../../../lib/db";
import { cookies } from "next/headers";
import { jwtVerify } from "jose";

// JWT Secret
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || "development-secret"
);

// Helper function to get user from token
async function getUserFromToken() {
  try {
    const cookieList = await cookies();
    const sessionToken = cookieList.get('auth-token')?.value;

    if (!sessionToken) {
      return null;
    }

    const { payload } = await jwtVerify(sessionToken, JWT_SECRET);
    return payload.user as { id: string; email: string; name: string };
  } catch (error) {
    return null;
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const user = await getUserFromToken();
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const prescription = await db.prescription.findFirst({
    where: {
      id: params.id,
      userId: user.id // Ensure user can only access their own prescriptions
    },
    include: {
      orders: true
    }
  });

  if (!prescription) {
    return NextResponse.json({ error: "Prescription not found" }, { status: 404 });
  }

  return NextResponse.json(prescription);
}
