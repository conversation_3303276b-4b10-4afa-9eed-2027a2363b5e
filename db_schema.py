import mysql.connector
from tabulate import tabulate

def connect_to_mysql():
    try:
        # Connect to MySQL server
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",  # Default XAMPP password is empty
            database="nplabs dev"  # Database name with space
        )
        return connection
    except mysql.connector.Error as err:
        print(f"Error connecting to MySQL: {err}")
        return None

def get_table_schema(connection):
    cursor = connection.cursor()
    
    # Get list of tables
    cursor.execute("SHOW TABLES")
    tables = [table[0] for table in cursor.fetchall()]
    
    schema_info = {}
    
    for table in tables:
        # Get column information
        cursor.execute(f"DESCRIBE {table}")
        columns = cursor.fetchall()
        
        # Get sample data (first 3 rows)
        cursor.execute(f"SELECT * FROM {table} LIMIT 3")
        sample_data = cursor.fetchall()
        
        # Get column names for the sample data
        column_names = [i[0] for i in cursor.description] if cursor.description else []
        
        schema_info[table] = {
            "columns": columns,
            "sample_data": sample_data,
            "column_names": column_names
        }
    
    cursor.close()
    return schema_info

def display_schema(schema_info):
    for table, info in schema_info.items():
        print(f"\n{'='*80}")
        print(f"TABLE: {table}")
        print('-'*80)
        
        try:
            # Display column information
            print("\nCOLUMNS:")
            print(tabulate(info['columns'], 
                          headers=['Field', 'Type', 'Null', 'Key', 'Default', 'Extra'],
                          tablefmt='grid'))
            
            # Display sample data if available
            if info['sample_data'] and info['column_names']:
                print(f"\nSAMPLE DATA (first 3 rows):")
                try:
                    print(tabulate(info['sample_data'], 
                                  headers=info['column_names'],
                                  tablefmt='grid'))
                except Exception as e:
                    print(f"Could not display sample data: {e}")
                    print("Column names:", info['column_names'])
                    print("Data sample:", info['sample_data'][:1])  # Show first row as example
            else:
                print("\nNo data to display or missing column names.")
            
        except Exception as e:
            print(f"\nError displaying table {table}: {e}")
        
        # Add some spacing between tables
        print("\n" + "="*80 + "\n")

def main():
    db_name = "nplabs dev"
    print(f"Connecting to MySQL database '{db_name}'...")
    connection = connect_to_mysql()
    
    if connection:
        try:
            print("Connected successfully!")
            print(f"\nFetching database schema for '{db_name}'...\n")
            schema = get_table_schema(connection)
            
            if not schema:
                print(f"No tables found in database '{db_name}'")
            else:
                print(f"Found {len(schema)} tables in database '{db_name}'\n")
                display_schema(schema)
                
        except mysql.connector.Error as err:
            print(f"\nMySQL Error: {err}")
            if err.errno == 1045:  # Access denied
                print("Please check your MySQL username and password.")
            elif err.errno == 1049:  # Unknown database
                print("The specified database does not exist.")
        except Exception as e:
            print(f"\nError: {e}")
        finally:
            connection.close()
            print("\nConnection closed.")
    else:
        print("Failed to connect to MySQL. Please check if MySQL server is running and the connection details are correct.")

if __name__ == "__main__":
    main()
