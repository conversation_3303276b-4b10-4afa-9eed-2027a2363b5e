import mysql.connector
from datetime import datetime, timedelta
import pandas as pd
from collections import Counter
import matplotlib.pyplot as plt

class PharmacyAnalyzer:
    def __init__(self):
        self.connection = None
        
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = mysql.connector.connect(
                host="localhost",
                user="root",
                password="",
                database="nplabs dev"
            )
            return True
        except mysql.connector.Error as err:
            print(f"Error connecting to database: {err}")
            return False
    
    def execute_query(self, query, params=None):
        """Execute a query and return results as a list of dictionaries"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, params or ())
            return cursor.fetchall()
        except mysql.connector.Error as err:
            print(f"Error executing query: {err}")
            return []
        finally:
            if 'cursor' in locals():
                cursor.close()
    
    def get_user_demographics(self):
        """Analyze user demographics"""
        # Get user count by country
        country_query = """
            SELECT country, COUNT(*) as user_count 
            FROM users 
            WHERE country IS NOT NULL AND country != ''
            GROUP BY country 
            ORDER BY user_count DESC
            LIMIT 10
        """
        
        # Get user growth over time
        growth_query = """
            SELECT 
                DATE(created_at) as signup_date,
                COUNT(*) as new_users,
                SUM(COUNT(*)) OVER (ORDER BY DATE(created_at)) as total_users
            FROM users
            WHERE created_at IS NOT NULL
            GROUP BY DATE(created_at)
            ORDER BY signup_date
        """
        
        return {
            'by_country': self.execute_query(country_query),
            'growth': self.execute_query(growth_query)
        }
    
    def get_medication_analysis(self):
        """Analyze medication orders"""
        # Most common medications (assuming there's a medications table)
        # This is a placeholder - adjust based on actual schema
        med_query = """
            SELECT 
                m.name as medication_name,
                COUNT(o.id) as order_count,
                COUNT(DISTINCT o.user_id) as unique_patients,
                AVG(oi.quantity) as avg_quantity
            FROM orders o
            JOIN order_items oi ON o.id = oi.order_id
            JOIN medications m ON oi.medication_id = m.id
            GROUP BY m.name
            ORDER BY order_count DESC
            LIMIT 10
        """
        
        # Alternative if no medications table - look for patterns in ticket subjects
        ticket_meds_query = """
            SELECT 
                LOWER(SUBSTRING_INDEX(SUBSTRING_INDEX(t.subject, ' ', 5), ' ', -1)) as possible_med,
                COUNT(*) as mention_count
            FROM tickets t
            WHERE t.subject REGEXP 'testosterone|viagra|sildenafil|tadalafil|estrogen|progesterone|thyroid|hgh|b12|melatonin|dhea|pregnenolone'
            GROUP BY possible_med
            HAVING mention_count > 5
            ORDER BY mention_count DESC
            LIMIT 15
        """
        
        return {
            'top_medications': self.execute_query(ticket_meds_query)
        }
    
    def get_prescription_analysis(self):
        """Analyze prescription patterns"""
        # Assuming there's a prescriptions table
        # This is a placeholder - adjust based on actual schema
        rx_query = """
            SELECT 
                DATE(created_at) as rx_date,
                COUNT(*) as rx_count,
                COUNT(DISTINCT user_id) as unique_patients
            FROM prescriptions
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY rx_date
            ORDER BY rx_date
        """
        
        # Alternative using ticket data
        rx_trend_query = """
            SELECT 
                DATE(created_at) as ticket_date,
                COUNT(*) as ticket_count,
                COUNT(DISTINCT user_id) as unique_patients
            FROM tickets
            WHERE subject LIKE '%prescrip%' 
               OR content LIKE '%prescrip%'
               OR subject LIKE '%rx%'
               OR content LIKE '%rx%'
            GROUP BY ticket_date
            ORDER BY ticket_date
        """
        
        return self.execute_query(rx_trend_query)
    
    def get_ticket_analysis(self):
        """Analyze support tickets for business insights"""
        # Ticket volume by category
        category_query = """
            SELECT 
                c.name as category,
                COUNT(t.id) as ticket_count,
                AVG(TIMESTAMPDIFF(HOUR, t.created_at, t.updated_at)) as avg_resolution_hours
            FROM tickets t
            JOIN categories c ON t.category_id = c.id
            GROUP BY c.name
            ORDER BY ticket_count DESC
        """
        
        # Common ticket subjects
        subject_query = """
            SELECT 
                LOWER(SUBSTRING_INDEX(SUBJECT, ' ', 5)) as subject_start,
                COUNT(*) as count
            FROM tickets
            GROUP BY subject_start
            HAVING count > 5
            ORDER BY count DESC
            LIMIT 10
        """
        
        return {
            'by_category': self.execute_query(category_query),
            'common_subjects': self.execute_query(subject_query)
        }
    
    def generate_report(self):
        """Generate comprehensive business analysis report"""
        print("\n" + "="*80)
        print("COMPOUNDING PHARMACY BUSINESS INTELLIGENCE REPORT")
        print("Generated on:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        print("="*80)
        
        # 1. User Analysis
        print("\n" + "="*80)
        print("USER DEMOGRAPHICS")
        print("="*80)
        
        user_data = self.get_user_demographics()
        
        print("\nTop Countries by User Count:")
        countries = pd.DataFrame(user_data['by_country'])
        if not countries.empty:
            print(countries.to_string(index=False))
        
        # 2. Medication Analysis
        print("\n" + "="*80)
        print("MEDICATION ANALYSIS")
        print("="*80)
        
        med_data = self.get_medication_analysis()
        
        print("\nMost Mentioned Medications in Tickets:")
        meds = pd.DataFrame(med_data['top_medications'])
        if not meds.empty:
            print(meds.to_string(index=False))
        
        # 3. Prescription Trends
        print("\n" + "="*80)
        print("PRESCRIPTION TRENDS")
        print("="*80)
        
        rx_trends = self.get_prescription_analysis()
        if rx_trends:
            trends = pd.DataFrame(rx_trends)
            if not trends.empty:
                print("\nTicket Volume Related to Prescriptions:")
                print(f"Total prescription-related tickets: {len(trends)}")
                print(f"Average daily tickets: {trends['ticket_count'].mean():.1f}")
        
        # 4. Support Analysis
        print("\n" + "="*80)
        print("SUPPORT TICKET ANALYSIS")
        print("="*80)
        
        ticket_data = self.get_ticket_analysis()
        
        if 'by_category' in ticket_data and ticket_data['by_category']:
            print("\nTickets by Category:")
            categories = pd.DataFrame(ticket_data['by_category'])
            print(categories.to_string(index=False))
        
        if 'common_subjects' in ticket_data and ticket_data['common_subjects']:
            print("\nMost Common Ticket Subjects:")
            subjects = pd.DataFrame(ticket_data['common_subjects'])
            print(subjects.to_string(index=False))
        
        # 5. Key Findings and Recommendations
        print("\n" + "="*80)
        print("KEY FINDINGS & RECOMMENDATIONS")
        print("="*80)
        
        print("""
1. User Base:
   - The platform has a growing user base with significant international reach.
   - Top countries should be prioritized for localized support and marketing.

2. Medication Trends:
   - The most mentioned medications in tickets suggest high demand areas.
   - Consider creating educational content around these medications.

3. Support Operations:
   - Analyze ticket categories to identify areas needing process improvements.
   - Common ticket subjects can reveal frequent user pain points.

4. Prescription Management:
   - Monitor prescription-related tickets for process improvements.
   - Consider implementing automation for common prescription requests.
""")

    def close(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()

def main():
    analyzer = PharmacyAnalyzer()
    
    if not analyzer.connect():
        print("Failed to connect to the database. Please check your connection settings.")
        return
    
    try:
        analyzer.generate_report()
    except Exception as e:
        print(f"An error occurred during analysis: {e}")
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
