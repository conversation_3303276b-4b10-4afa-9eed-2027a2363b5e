# Architecture Document: Prescription Dashboard

## 1. System Overview
The Prescription Dashboard is a Next.js application that provides a secure interface for users to track their prescription status through a multi-stage compounding process.

## 2. Architecture Diagram
```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Client Browser │────▶│  Next.js App    │────▶│  MySQL Database │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                              │
                              │
                              ▼
                        ┌─────────────────┐
                        │                 │
                        │  Prisma ORM     │
                        │                 │
                        └─────────────────┘
```

## 3. Component Architecture

### 3.1 Frontend Components
- **Layout**: Root layout with authentication wrapper
- **Auth Components**: Login form, session provider
- **Dashboard Components**: Prescription list, detail view
- **UI Components**: Stepper, cards, upload form

### 3.2 Backend Services
- **API Routes**: RESTful endpoints for data access
- **Authentication**: JWT-based auth with middleware protection
- **Database Access**: Prisma client for data operations

## 4. Data Flow

### 4.1 Authentication Flow
1. User submits credentials to `/api/auth/[...nextauth]`
2. Server validates credentials against database
3. JWT token issued and stored in cookies
4. Middleware validates token on protected routes

### 4.2 Dashboard Data Flow
1. Client requests data from `/api/prescriptions`
2. Server validates authentication via middleware
3. Prisma queries database for user's prescriptions
4. Data returned to client for rendering

### 4.3 Prescription Detail Flow
1. User selects prescription from dashboard
2. Client requests details from `/api/prescriptions/[id]`
3. Server fetches prescription data and status information
4. Client renders detailed view with status stepper

## 5. Database Schema
Key tables used by the application:
- `User`: Authentication and profile data
- `prescriptions`: Prescription records with status
- `orders`: Order information related to prescriptions

## 6. Security Architecture
- JWT-based authentication
- Route protection via middleware
- HTTPS for all communications
- Database credentials stored in environment variables
- Input validation on all API endpoints

## 7. Deployment Architecture
- Vercel for hosting and CI/CD
- Environment-specific configurations
- Database connection pooling for performance