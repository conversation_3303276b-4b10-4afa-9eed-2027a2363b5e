generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @default(cuid())
  name          String?
  email         String         @unique
  password      String
  emailVerified DateTime?
  image         String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  prescriptions Prescription[]
}

model Prescription {
  id              String   @id @default(cuid())
  userId          String
  prescriptionImg String
  description     String?
  status          String   @default("submitted")
  amount          String?
  comments        String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders          Order[]
}

model Order {
  id             String       @id @default(cuid())
  orderId        String       @unique
  prescriptionId String
  particulars    String
  paymentStatus  String?      @default("pending")
  trackingId     String?
  trackingLink   String?
  deliverStatus  String?      @default("processing")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  prescription   Prescription @relation(fields: [prescriptionId], references: [id], onDelete: Cascade)
}


