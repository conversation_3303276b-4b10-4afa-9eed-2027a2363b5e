import mysql.connector
from collections import defaultdict

DB_CONFIG = {
    'host': "localhost",
    'user': "root",
    'password': "",
    'database': "nplabs dev"
}

TOP_N_PRESCRIPTIONS = 10  # Number of top prescriptions to analyze
TOP_M_COUNTRIES = 5     # Number of top countries to show for each top prescription

def get_db_connection():
    return mysql.connector.connect(**DB_CONFIG)

def analyze_prescriptions_by_country(cursor):
    query = """
        SELECT 
            LOWER(TRIM(p.description)) as prescription_description, 
            u.country, 
            COUNT(*) as prescription_count
        FROM prescriptions p
        JOIN users u ON p.user_id = u.id
        WHERE p.description IS NOT NULL AND p.description != '' AND u.country IS NOT NULL
        GROUP BY prescription_description, u.country
        ORDER BY prescription_count DESC, prescription_description, u.country;
    """
    cursor.execute(query)
    return cursor.fetchall()

def main():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        print("Analyzing prescription data by country...\n")
        results = analyze_prescriptions_by_country(cursor)

        if not results:
            print("No prescription data found or no linkable user data with countries.")
            return

        # Aggregate total counts for each prescription globally
        prescription_global_counts = defaultdict(int)
        # Aggregate counts for each prescription by country
        prescription_country_details = defaultdict(lambda: defaultdict(int))

        for row in results:
            desc = row['prescription_description']
            country = row['country']
            count = row['prescription_count']
            
            prescription_global_counts[desc] += count
            prescription_country_details[desc][country] += count
        
        # Sort prescriptions by global count
        sorted_global_prescriptions = sorted(prescription_global_counts.items(), key=lambda item: item[1], reverse=True)

        print(f"--- Top {TOP_N_PRESCRIPTIONS} Most Common Prescriptions Globally ---")
        for i, (desc, total_count) in enumerate(sorted_global_prescriptions):
            if i >= TOP_N_PRESCRIPTIONS:
                break
            print(f"\n{i+1}. {desc.capitalize()} (Total: {total_count})")
            
            # Get country details for this prescription
            country_stats = prescription_country_details[desc]
            sorted_country_stats = sorted(country_stats.items(), key=lambda item: item[1], reverse=True)
            
            print(f"  Top {TOP_M_COUNTRIES} countries for this prescription:")
            if not sorted_country_stats:
                print("    No specific country data for this prescription.")
            for j, (country, count) in enumerate(sorted_country_stats):
                if j >= TOP_M_COUNTRIES:
                    if len(sorted_country_stats) > TOP_M_COUNTRIES:
                        print("    ...")
                    break
                print(f"    - {country}: {count} prescriptions")
        
        if not sorted_global_prescriptions:
            print("No prescription data to analyze after aggregation.")

    except mysql.connector.Error as err:
        print(f"Database error: {err}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    main()
