{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_65fe3fd6._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_12a3b8ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "x2B8J76hFhZboTFKgchAg0WeHWNefWaZNfWHNaxb8bM=", "__NEXT_PREVIEW_MODE_ID": "887888cab145efb7d4e00fb2e781e3b7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6e0fc35c09e4b58642d4a220e930df0650032809183dd59cf7b09ebb1428fd00", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0eb57860922f77f31d72cf83f61d0f0b857b3645744ac01026d588683615b233"}}}, "sortedMiddleware": ["/"], "functions": {}}