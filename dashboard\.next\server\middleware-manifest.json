{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_65fe3fd6._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_12a3b8ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "x2B8J76hFhZboTFKgchAg0WeHWNefWaZNfWHNaxb8bM=", "__NEXT_PREVIEW_MODE_ID": "39bf2b6d96fa673ec31ede08cdba8dfa", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "287125ca6c75d2fe207358d40acd869b4a7ea9593572e3560518f79f1748e9ca", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4338204a854dac15a61ce0ab234735dee14fca57e66ab36a72a0c3caf1c1bf03"}}}, "sortedMiddleware": ["/"], "functions": {}}