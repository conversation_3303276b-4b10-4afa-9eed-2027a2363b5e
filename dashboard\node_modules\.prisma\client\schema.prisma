generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  password      String
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model activity_log {
  id            BigInt         @id @default(autoincrement()) @db.UnsignedBigInt
  prescription  BigInt?        @db.UnsignedBigInt
  ticket        BigInt?        @db.UnsignedBigInt
  user          BigInt?        @db.UnsignedBigInt
  activities    String         @db.LongText
  old_data      String?        @db.LongText
  current_data  String?        @db.LongText
  created_at    DateTime?      @db.Timestamp(0)
  updated_at    DateTime?      @db.Timestamp(0)
  prescriptions prescriptions? @relation(fields: [prescription], references: [id], map: "activity_log_prescription_foreign")
  tickets       tickets?       @relation(fields: [ticket], references: [id], map: "activity_log_ticket_foreign")
  users         users?         @relation(fields: [user], references: [id], map: "activity_log_user_foreign")

  @@index([prescription], map: "activity_log_prescription_foreign")
  @@index([ticket], map: "activity_log_ticket_foreign")
  @@index([user], map: "activity_log_user_foreign")
}

model activity_logs {
  id          BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id     Int?
  message     String    @db.VarChar(255)
  module_name String    @db.VarChar(255)
  url         String    @db.VarChar(255)
  request     String?   @db.VarChar(255)
  method      String    @db.VarChar(255)
  ip          String    @db.VarChar(255)
  agent       String?   @db.VarChar(255)
  created_at  DateTime? @db.Timestamp(0)
  updated_at  DateTime? @db.Timestamp(0)
}

model category {
  id    BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  name  String  @db.VarChar(255)
  color String? @db.VarChar(255)
}

model clinic {
  id                            BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  user_id                       BigInt    @db.UnsignedBigInt
  clinic_id                     BigInt    @db.UnsignedBigInt
  created_at                    DateTime? @db.Timestamp(0)
  updated_at                    DateTime? @db.Timestamp(0)
  users_clinic_clinic_idTousers users     @relation("clinic_clinic_idTousers", fields: [clinic_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "clinic_clinic_id_foreign")
  users_clinic_user_idTousers   users     @relation("clinic_user_idTousers", fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "clinic_user_id_foreign")

  @@index([clinic_id], map: "clinic_clinic_id_foreign")
  @@index([user_id], map: "clinic_user_id_foreign")
}

model emailtexts {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  email      String    @unique(map: "emailtexts_email_unique") @db.VarChar(255)
  subject    String    @db.VarChar(255)
  greeting   String    @db.VarChar(255)
  body       String    @db.LongText
  havecta    Boolean?
  ctatext    String?   @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
}

model failed_jobs {
  id         BigInt   @id @default(autoincrement()) @db.UnsignedBigInt
  uuid       String   @unique(map: "failed_jobs_uuid_unique") @db.VarChar(255)
  connection String   @db.Text
  queue      String   @db.Text
  payload    String   @db.LongText
  exception  String   @db.LongText
  failed_at  DateTime @default(now()) @db.Timestamp(0)
}

model feedback {
  id          BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  feedback_id String    @unique(map: "feedback_feedback_id_unique") @db.VarChar(255)
  content     String    @db.LongText
  attachment  String?   @db.VarChar(255)
  user        BigInt?   @db.UnsignedBigInt
  status      String    @db.VarChar(255)
  archive_at  DateTime? @db.Timestamp(0)
  created_at  DateTime? @db.Timestamp(0)
  updated_at  DateTime? @db.Timestamp(0)
  users       users?    @relation(fields: [user], references: [id], map: "feedback_user_foreign")

  @@index([user], map: "feedback_user_foreign")
}

model migrations {
  id        Int    @id @default(autoincrement()) @db.UnsignedInt
  migration String @db.VarChar(255)
  batch     Int
}

model notifications {
  id              String    @id @db.Char(36)
  type            String    @db.VarChar(255)
  notifiable_type String    @db.VarChar(255)
  notifiable_id   BigInt    @db.UnsignedBigInt
  data            String    @db.Text
  read_at         DateTime? @db.Timestamp(0)
  created_at      DateTime? @db.Timestamp(0)
  updated_at      DateTime? @db.Timestamp(0)

  @@index([notifiable_type, notifiable_id], map: "notifications_notifiable_type_notifiable_id_index")
}

model orders {
  id                      BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  order_id                String    @db.VarChar(255)
  user_id                 BigInt?   @db.UnsignedBigInt
  prescription_id         BigInt?   @db.UnsignedBigInt
  particulars             String    @db.LongText
  paymentlink             String?   @db.VarChar(255)
  payment_intent_id       String?   @db.VarChar(255)
  paymentstatus           String?   @db.VarChar(255)
  paymentprof             String?   @db.VarChar(255)
  trackingid              String?   @db.VarChar(255)
  trackinglink            String?   @db.VarChar(255)
  comments                String?   @db.VarChar(255)
  deliverstatus           String?   @db.VarChar(255)
  created_at              DateTime? @db.Timestamp(0)
  updated_at              DateTime? @db.Timestamp(0)
  payment_done            DateTime? @db.Date
  medication_created      DateTime? @db.Date
  payment_link_created_at DateTime? @db.Date
  delivered_date          DateTime? @db.Date

  @@index([prescription_id], map: "orders_prescription_id_foreign")
  @@index([user_id], map: "orders_user_id_foreign")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model password_resets {
  email      String    @db.VarChar(255)
  token      String    @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)

  @@index([email], map: "password_resets_email_index")
  @@ignore
}

model patient {
  user_id    BigInt @db.UnsignedBigInt
  patient_id BigInt @db.UnsignedBigInt

  @@id([patient_id, user_id])
  @@index([user_id], map: "patient_user_id_foreign")
}

model permissions {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  name       String    @db.VarChar(255)
  slug       String    @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
}

model personal_access_tokens {
  id             BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  tokenable_type String    @db.VarChar(255)
  tokenable_id   BigInt    @db.UnsignedBigInt
  name           String    @db.VarChar(255)
  token          String    @unique(map: "personal_access_tokens_token_unique") @db.VarChar(64)
  abilities      String?   @db.Text
  last_used_at   DateTime? @db.Timestamp(0)
  created_at     DateTime? @db.Timestamp(0)
  updated_at     DateTime? @db.Timestamp(0)

  @@index([tokenable_type, tokenable_id], map: "personal_access_tokens_tokenable_type_tokenable_id_index")
}

model prescriber {
  user_id       BigInt @db.UnsignedBigInt
  prescriber_id BigInt @db.UnsignedBigInt

  @@id([user_id, prescriber_id])
  @@index([prescriber_id], map: "prescriber_prescriber_id_foreign")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model prescription_queries {
  id                           BigInt                         @id @default(autoincrement()) @db.UnsignedBigInt
  prescription_id              BigInt                         @db.UnsignedBigInt
  patient_id                   BigInt                         @db.UnsignedBigInt
  status                       Boolean                        @default(false)
  created_at                   DateTime?                      @db.Timestamp(0)
  updated_at                   DateTime?                      @db.Timestamp(0)
  users                        users                          @relation(fields: [patient_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "prescription_queries_patient_id_foreign")
  prescriptions                prescriptions                  @relation(fields: [prescription_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "prescription_queries_prescription_id_foreign")
  prescription_query_responses prescription_query_responses[]

  @@index([patient_id], map: "prescription_queries_patient_id_foreign")
  @@index([prescription_id], map: "prescription_queries_prescription_id_foreign")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model prescription_query_responses {
  id                    BigInt               @id @default(autoincrement()) @db.UnsignedBigInt
  prescription_query_id BigInt               @db.UnsignedBigInt
  sender_id             BigInt               @db.UnsignedBigInt
  message               String               @db.LongText
  status                Int                  @default(0) @db.TinyInt
  created_at            DateTime?            @db.Timestamp(0)
  updated_at            DateTime?            @db.Timestamp(0)
  prescription_queries  prescription_queries @relation(fields: [prescription_query_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "prescription_query_responses_prescription_query_id_foreign")

  @@index([prescription_query_id], map: "prescription_query_responses_prescription_query_id_foreign")
  @@index([sender_id], map: "prescription_query_responses_sender_id_foreign")
}

model prescriptions {
  id                   BigInt                 @id @default(autoincrement()) @db.UnsignedBigInt
  user_id              BigInt?                @db.UnsignedBigInt
  prescriber_id        BigInt?                @db.UnsignedBigInt
  pharmacy_id          Int?
  clinic_id            BigInt?                @db.UnsignedBigInt
  description          String?                @db.VarChar(255)
  prescription_img     String                 @db.VarChar(255)
  numberofrefill       String?                @db.VarChar(255)
  amount               String?                @db.VarChar(255)
  status               String                 @db.VarChar(255)
  comments             String?                @db.VarChar(255)
  approved_by_user     String                 @db.VarChar(255)
  order_id             Int?                   @db.UnsignedInt
  re_order_id          Int?
  merge_to             String?                @db.LongText
  merge_with           Int?
  payment_type         String?                @db.VarChar(100)
  archive_at           DateTime?              @db.Timestamp(0)
  created_at           DateTime?              @db.Timestamp(0)
  updated_at           DateTime?              @db.Timestamp(0)
  activity_log         activity_log[]
  prescription_queries prescription_queries[]

  @@index([prescriber_id], map: "prescriptions_prescriber_id_foreign")
  @@index([user_id], map: "prescriptions_user_id_foreign")
}

model priority {
  id    BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  name  String  @db.VarChar(255)
  color String? @db.VarChar(255)
}

model role_user {
  user_id    BigInt    @db.UnsignedBigInt
  role_id    BigInt    @db.UnsignedBigInt
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)

  @@id([user_id, role_id])
  @@index([role_id], map: "role_user_role_id_foreign")
}

model roles {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  role       String    @unique(map: "roles_role_unique") @db.VarChar(255)
  slug       String    @db.VarChar(255)
  created_at DateTime? @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)
}

model roles_permissions {
  role_id       BigInt @db.UnsignedBigInt
  permission_id BigInt @db.UnsignedBigInt

  @@id([role_id, permission_id])
  @@index([permission_id], map: "roles_permissions_permission_id_foreign")
}

model status {
  id    BigInt  @id @default(autoincrement()) @db.UnsignedBigInt
  name  String  @db.VarChar(255)
  color String? @db.VarChar(255)
}

model ticket_comments {
  id         BigInt    @id @default(autoincrement()) @db.UnsignedBigInt
  content    String    @db.LongText
  html       String?   @db.LongText
  user_id    BigInt?   @db.UnsignedBigInt
  ticket_id  BigInt?   @db.UnsignedBigInt
  recent     Boolean?
  created_at DateTime? @default(now()) @db.Timestamp(0)
  updated_at DateTime? @db.Timestamp(0)

  @@index([ticket_id], map: "ticket_comments_ticket_id_foreign")
  @@index([user_id], map: "ticket_comments_user_id_foreign")
}

model tickets {
  id           BigInt         @id @default(autoincrement()) @db.UnsignedBigInt
  track_id     String         @db.VarChar(255)
  subject      String         @db.VarChar(255)
  content      String         @db.LongText
  html         String?        @db.LongText
  ticketimg    String         @db.VarChar(255)
  status_id    BigInt?        @db.UnsignedBigInt
  priority_id  BigInt?        @db.UnsignedBigInt
  user_id      BigInt?        @db.UnsignedBigInt
  staff_id     BigInt?        @db.UnsignedBigInt
  category_id  BigInt?        @db.UnsignedBigInt
  archive_at   DateTime?      @db.Timestamp(0)
  created_at   DateTime?      @db.Timestamp(0)
  updated_at   DateTime?      @db.Timestamp(0)
  completed_at DateTime?      @db.Timestamp(0)
  activity_log activity_log[]

  @@index([category_id], map: "tickets_category_id_foreign")
  @@index([priority_id], map: "tickets_priority_id_foreign")
  @@index([staff_id], map: "tickets_staff_id_foreign")
  @@index([status_id], map: "tickets_status_id_foreign")
  @@index([user_id], map: "tickets_user_id_foreign")
}

model users {
  id                             BigInt                 @id @default(autoincrement()) @db.UnsignedBigInt
  dob                            DateTime?              @db.Date
  fname                          String                 @db.VarChar(255)
  lname                          String                 @db.VarChar(255)
  email                          String                 @unique(map: "users_email_unique") @db.VarChar(255)
  profileimg                     String?                @db.VarChar(255)
  email_verified_at              DateTime               @default(now()) @db.Timestamp(0)
  password                       String                 @db.VarChar(255)
  country                        String?                @db.VarChar(255)
  city                           String?                @db.VarChar(255)
  address                        String?                @db.VarChar(255)
  postcode                       String?                @db.VarChar(255)
  phone                          String?                @db.VarChar(255)
  company                        String?                @db.VarChar(255)
  extension                      BigInt?
  medical_license_no             String?                @db.VarChar(225)
  country_medical_license        String?                @db.VarChar(225)
  by_affiliate                   BigInt?                @db.UnsignedBigInt
  approved_at                    DateTime?              @db.Timestamp(0)
  remember_token                 String?                @db.VarChar(100)
  created_at                     DateTime?              @db.Timestamp(0)
  updated_at                     DateTime?              @db.Timestamp(0)
  activity_log                   activity_log[]
  clinic_clinic_clinic_idTousers clinic[]               @relation("clinic_clinic_idTousers")
  clinic_clinic_user_idTousers   clinic[]               @relation("clinic_user_idTousers")
  feedback                       feedback[]
  prescription_queries           prescription_queries[]
}

model users_permissions {
  user_id       BigInt @db.UnsignedBigInt
  permission_id BigInt @db.UnsignedBigInt

  @@id([user_id, permission_id])
  @@index([permission_id], map: "users_permissions_permission_id_foreign")
}
