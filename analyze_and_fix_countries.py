import mysql.connector
from collections import defaultdict
import re

def get_db_connection():
    """Create a database connection"""
    return mysql.connector.connect(
        host="localhost",
        user="root",
        password="",
        database="nplabs dev"
    )

def get_unique_countries():
    """Retrieve all unique country names from the users table"""
    conn = get_db_connection()
    cursor = conn.cursor(dictionary=True)
    
    try:
        cursor.execute("SELECT DISTINCT country FROM users WHERE country IS NOT NULL ORDER BY country")
        return [row['country'] for row in cursor.fetchall()]
    finally:
        cursor.close()
        conn.close()

def standardize_country_name(country):
    """Standardize a single country name using AI knowledge"""
    if not country or not country.strip():
        return None
    
    # Clean the input
    cleaned = country.strip()
    
    # Common fixes
    cleaned = re.sub(r'\s+', ' ', cleaned)  # Normalize spaces
    
    # Common patterns and their corrections
    patterns = {
        # Common misspellings and variations
        r'(?i)united\s*states(?:\s+of\s+america)?|\busa?\b|\bu\.?s\.?a?\.?': 'United States',
        r'(?i)united\s*kingdom|\buk\b|\bu\.?k\.?|great\s+britain|england|scotland|wales|northern ireland': 'United Kingdom',
        r'(?i)espa[ñn]a': 'Spain',
        r'(?i)deutschland': 'Germany',
        r'(?i)france|francia|frankreich': 'France',
        r'(?i)italy|italia|italie|italien': 'Italy',
        r'(?i)netherlands|holland|nederland': 'Netherlands',
        r'(?i)belgium|belgique|belgie|belgien|belgica|belgio': 'Belgium',
        r'(?i)greece|ελλάδα|ελλαδα|ελλας|grecia|grèce|griechenland': 'Greece',
        
        # Country codes
        r'(?i)^us$|^usa$': 'United States',
        r'(?i)^uk$|^gb$': 'United Kingdom',
        r'(?i)^de$': 'Germany',
        r'(?i)^fr$': 'France',
        r'(?i)^it$': 'Italy',
        r'(?i)^es$': 'Spain',
        r'(?i)^nl$': 'Netherlands',
        r'(?i)^be$': 'Belgium',
        r'(?i)^gr$': 'Greece',
        
        # Common typos and variations
        r'(?i)america': 'United States',
        r'(?i)great britian': 'United Kingdom',
        r'(?i)england|britain': 'United Kingdom',
        r'(?i)scotland': 'United Kingdom',
        r'(?i)wales': 'United Kingdom',
        r'(?i)northern ireland': 'United Kingdom',
        r'(?i)espana': 'Spain',
        r'(?i)deutchland': 'Germany',
        r'(?i)french': 'France',
        r'(?i)italian': 'Italy',
        r'(?i)dutch': 'Netherlands',
        r'(?i)belgian': 'Belgium',
        r'(?i)greek': 'Greece',
        
        # Handle common city/country confusions
        r'(?i)^london$': 'United Kingdom',
        r'(?i)^paris$': 'France',
        r'(?i)^berlin$': 'Germany',
        r'(?i)^rome$': 'Italy',
        r'(?i)^madrid$': 'Spain',
        r'(?i)^amsterdam$': 'Netherlands',
        r'(?i)^brussels$': 'Belgium',
        r'(?i)^athens$': 'Greece',
        
        # Handle common abbreviations
        r'(?i)^u\.?s\.?(?:a\.?)?$': 'United States',
        r'(?i)^u\.?k\.?$': 'United Kingdom',
        r'(?i)^u\.?s\.?s\.?r\.?$': 'Russia',  # Historical, but might appear
    }
    
    # Check for exact matches first
    for pattern, standard in patterns.items():
        if re.fullmatch(pattern, cleaned, re.IGNORECASE):
            return standard
    
    # Check for partial matches
    for pattern, standard in patterns.items():
        if re.search(pattern, cleaned, re.IGNORECASE):
            return standard
    
    # If no match found, return the cleaned version with proper capitalization
    return ' '.join(word.capitalize() for word in cleaned.split())

def generate_fix_queries(countries):
    """Generate SQL queries to fix the country names"""
    queries = []
    updates = defaultdict(list)
    
    # Group countries by their standardized name
    for country in countries:
        std_country = standardize_country_name(country)
        if std_country and std_country != country:
            updates[std_country].append(country)
    
    # Generate UPDATE queries
    for std_country, variants in updates.items():
        # Handle special characters in SQL
        safe_variants = [f"'{v.replace('\\', '\\\\').replace("'", "''")}'" for v in variants]
        variants_str = ', '.join(safe_variants)
        
        query = f"""
        UPDATE users 
        SET country = '{std_country.replace("'", "''")}'
        WHERE country IN ({variants_str})
        """
        queries.append((std_country, variants, query.strip()))
    
    return queries

def main():
    print("Retrieving unique country names...")
    countries = get_unique_countries()
    print(f"Found {len(countries)} unique country names")
    
    print("\nAnalyzing and standardizing country names...")
    standardized = {}
    for country in countries:
        std = standardize_country_name(country)
        if std != country:
            if std not in standardized:
                standardized[std] = []
            standardized[std].append(country)
    
    # Print the standardization mapping
    print("\nStandardization mapping:")
    print("-" * 60)
    for std, variants in sorted(standardized.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{std}: {', '.join(variants[:5])}" + ("..." if len(variants) > 5 else ""))
    
    # Generate and print the SQL queries
    print("\nSQL queries to fix the country names:")
    print("-" * 60)
    queries = generate_fix_queries(countries)
    
    for std_country, variants, query in queries:
        print(f"-- Standardizing {len(variants)} variants to '{std_country}'")
        print(query)
        print("GO\n")
    
    print(f"\nTotal unique countries after standardization: {len(set(standardize_country_name(c) for c in countries))}")

if __name__ == "__main__":
    main()
