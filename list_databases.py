import mysql.connector

def list_databases():
    try:
        # Connect to MySQL server without specifying a database
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""  # Default XAMPP password is empty
        )
        
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES")
        
        print("\nAvailable databases:")
        print("-" * 50)
        for (database,) in cursor:
            print(f"- {database}")
            
        cursor.close()
        connection.close()
        
    except mysql.connector.Error as err:
        print(f"Error: {err}")

if __name__ == "__main__":
    list_databases()
