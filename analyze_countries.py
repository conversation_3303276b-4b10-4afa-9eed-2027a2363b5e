import mysql.connector
from collections import Counter

def analyze_countries():
    try:
        # Connect to the database
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="",
            database="nplabs dev"
        )
        
        cursor = conn.cursor(dictionary=True)
        
        # Get all unique country values
        cursor.execute("SELECT DISTINCT country FROM users WHERE country IS NOT NULL")
        countries = [row['country'] for row in cursor.fetchall()]
        
        print(f"Total unique country values: {len(countries)}")
        
        # Count occurrences of each country
        cursor.execute("""
            SELECT 
                country, 
                COUNT(*) as user_count 
            FROM users 
            WHERE country IS NOT NULL 
            GROUP BY country 
            ORDER BY user_count DESC
        """)
        
        print("\nTop 20 most common country values:")
        print("-" * 50)
        for i, row in enumerate(cursor.fetchall()):
            if i >= 20:
                break
            print(f"{row['country']}: {row['user_count']} users")
        
        # Check for potential data quality issues
        print("\nPotential data quality issues:")
        print("-" * 50)
        
        # Check for empty strings
        cursor.execute("SELECT COUNT(*) as empty_count FROM users WHERE country = ''")
        empty = cursor.fetchone()
        print(f"Empty country strings: {empty['empty_count']}")
        
        # Check for NULL values
        cursor.execute("SELECT COUNT(*) as null_count FROM users WHERE country IS NULL")
        nulls = cursor.fetchone()
        print(f"NULL country values: {nulls['null_count']}")
        
        # Check for variations of the same country
        cursor.execute("""
            SELECT 
                LOWER(TRIM(country)) as clean_country,
                COUNT(*) as count
            FROM users 
            WHERE country IS NOT NULL AND country != ''
            GROUP BY LOWER(TRIM(country))
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 10
        """)
        
        print("\nMost common country values (case-insensitive, trimmed):")
        print("-" * 50)
        for row in cursor.fetchall():
            print(f"{row['clean_country']}: {row['count']} users")
        
        # Check for country names that might be cities or other data
        cursor.execute("""
            SELECT DISTINCT country
            FROM users
            WHERE 
                country IS NOT NULL AND 
                country != '' AND
                LENGTH(country) > 30
            LIMIT 10
        """)
        
        print("\nPotentially problematic long country names:")
        print("-" * 50)
        for row in cursor.fetchall():
            print(f"- {row['country']}")
        
        cursor.close()
        conn.close()
        
    except mysql.connector.Error as err:
        print(f"Error: {err}")

if __name__ == "__main__":
    analyze_countries()
