import mysql.connector
import re
from datetime import datetime
from collections import Counter

# --- Database Configuration ---
DB_CONFIG = {
    'host': "localhost",
    'user': "root",
    'password': "",
    'database': "nplabs dev"
}

# --- Country Standardization Mapping ---
# This is a more comprehensive map. Keys should be lowercase and stripped.
COUNTRY_MAPPING = {
    # United States
    'united states of america': 'United States',
    'usa': 'United States',
    'u.s.a.': 'United States',
    'u.s.': 'United States',
    'us': 'United States',
    'america': 'United States',
    'states': 'United States',
    'united states': 'United States',
    'california': 'United States',
    'new york': 'United States',
    'texas': 'United States',
    'florida': 'United States',
    # Add more US states if they frequently appear as countries

    # United Kingdom
    'united kingdom': 'United Kingdom',
    'uk': 'United Kingdom',
    'u.k.': 'United Kingdom',
    'great britain': 'United Kingdom',
    'gb': 'United Kingdom',
    'england': 'United Kingdom',
    'britain': 'United Kingdom',
    'scotland': 'United Kingdom',
    'wales': 'United Kingdom',
    'northern ireland': 'United Kingdom',
    'london': 'United Kingdom',

    # Germany
    'germany': 'Germany',
    'deutschland': 'Germany',
    'de': 'Germany',
    'germ': 'Germany',
    'berlin': 'Germany',

    # France
    'france': 'France',
    'fr': 'France',
    'frankreich': 'France',
    'paris': 'France',

    # Italy
    'italy': 'Italy',
    'italia': 'Italy',
    'it': 'Italy',
    'rome': 'Italy',

    # Spain
    'spain': 'Spain',
    'españa': 'Spain',
    'espana': 'Spain',
    'es': 'Spain',
    'madrid': 'Spain',

    # Canada
    'canada': 'Canada',
    'ca': 'Canada',

    # Australia
    'australia': 'Australia',
    'au': 'Australia',

    # Netherlands
    'netherlands': 'Netherlands',
    'holland': 'Netherlands',
    'nederland': 'Netherlands',
    'nl': 'Netherlands',
    'the netherlands': 'Netherlands',
    'pays bas': 'Netherlands',
    'amsterdam': 'Netherlands',
    
    # Belgium
    'belgium': 'Belgium',
    'belgique': 'Belgium',
    'belgië': 'Belgium',
    'be': 'Belgium',
    'brussels': 'Belgium',

    # Greece
    'greece': 'Greece',
    'ελλάδα': 'Greece',
    'ελλαδα': 'Greece',
    'gr': 'Greece',
    'hellas': 'Greece',
    'athens': 'Greece',
    
    # Switzerland
    'switzerland': 'Switzerland',
    'schweiz': 'Switzerland',
    'suisse': 'Switzerland',
    'ch': 'Switzerland',

    # Austria
    'austria': 'Austria',
    'österreich': 'Austria',
    'at': 'Austria',

    # Portugal
    'portugal': 'Portugal',
    'pt': 'Portugal',

    # Ireland
    'ireland': 'Ireland',
    'eire': 'Ireland',
    'ie': 'Ireland',

    # Sweden
    'sweden': 'Sweden',
    'sverige': 'Sweden',
    'se': 'Sweden',

    # Norway
    'norway': 'Norway',
    'norge': 'Norway',
    'no': 'Norway',

    # Denmark
    'denmark': 'Denmark',
    'danmark': 'Denmark',
    'dk': 'Denmark',

    # Finland
    'finland': 'Finland',
    'suomi': 'Finland',
    'fi': 'Finland',
    
    # Poland
    'poland': 'Poland',
    'polska': 'Poland',
    'pl': 'Poland',

    # Romania
    'romania': 'Romania',
    'românia': 'Romania',
    'ro': 'Romania',

    # Czech Republic
    'czech republic': 'Czech Republic',
    'ceska republika': 'Czech Republic',
    'czechia': 'Czech Republic',
    'cz': 'Czech Republic',

    # Hungary
    'hungary': 'Hungary',
    'magyarország': 'Hungary',
    'hu': 'Hungary',
    
    # Japan
    'japan': 'Japan',
    'nippon': 'Japan',
    'nihon': 'Japan',
    'jp': 'Japan',

    # China
    'china': 'China',
    'peoples republic of china': 'China',
    'prc': 'China',
    'cn': 'China',

    # India
    'india': 'India',
    'in': 'India',

    # Brazil
    'brazil': 'Brazil',
    'brasil': 'Brazil',
    'br': 'Brazil',

    # Russia
    'russia': 'Russia',
    'russian federation': 'Russia',
    'россия': 'Russia',
    'ru': 'Russia',

    # South Africa
    'south africa': 'South Africa',
    'za': 'South Africa',
    
    # Cyprus
    'cyprus': 'Cyprus',
    'cy': 'Cyprus',
    'κύπρος': 'Cyprus',
    'kibris': 'Cyprus',
    
    # Peru
    'peru': 'Peru',
    'pe': 'Peru',

    # Other common fixes
    'united arab emirates': 'United Arab Emirates',
    'uae': 'United Arab Emirates',
    'new zealand': 'New Zealand',
    'nz': 'New Zealand',
    'south korea': 'South Korea',
    'republic of korea': 'South Korea',
    'kr': 'South Korea',
    'north korea': 'North Korea',
    'dprk': 'North Korea',
    'kp': 'North Korea',
}

# Patterns to identify garbage entries (to be set to NULL)
# This can be improved with more sophisticated checks
GARBAGE_PATTERNS = [
    re.compile(r'^[a-z0-9]{10,}$'),  # Long alphanumeric strings without spaces
    re.compile(r'^[^a-zA-Z]*$'),       # Contains no letters
    re.compile(r'(.)\1{4,}'),          # Repeated characters like 'aaaaa'
    re.compile(r'^[bcdfghjklmnpqrstvwxyz]{5,}$', re.IGNORECASE), # All consonants (min 5)
]

# Known valid country names (for final check, case-insensitive)
# This list can be expanded or loaded from a file/API for more accuracy
KNOWN_COUNTRIES_LOWER = {name.lower() for name in COUNTRY_MAPPING.values()} | {
    'afghanistan', 'albania', 'algeria', 'andorra', 'angola', 'argentina', 
    'armenia', 'aruba', 'azerbaijan', 'bahamas', 'bahrain', 'bangladesh', 
    'barbados', 'belarus', 'belize', 'benin', 'bhutan', 'bolivia', 
    'bosnia and herzegovina', 'botswana', 'brunei', 'bulgaria', 'burkina faso', 
    'burundi', 'cabo verde', 'cambodia', 'cameroon', 'central african republic', 
    'chad', 'chile', 'colombia', 'comoros', 'congo (congo-brazzaville)', 
    'costa rica', 'croatia', 'cuba', 'curacao', 'cyprus', 'djibouti', 'dominica', 
    'dominican republic', 'ecuador', 'egypt', 'el salvador', 'equatorial guinea', 
    'eritrea', 'estonia', 'eswatini', 'ethiopia', 'fiji', 'gabon', 'gambia', 
    'georgia', 'ghana', 'grenada', 'guatemala', 'guinea', 'guinea-bissau', 
    'guyana', 'haiti', 'honduras', 'iceland', 'indonesia', 'iran', 'iraq', 
    'israel', 'jamaica', 'jordan', 'kazakhstan', 'kenya', 'kiribati', 'kosovo', 
    'kuwait', 'kyrgyzstan', 'laos', 'latvia', 'lebanon', 'lesotho', 'liberia', 
    'libya', 'liechtenstein', 'lithuania', 'luxembourg', 'madagascar', 'malawi', 
    'malaysia', 'maldives', 'mali', 'malta', 'marshall islands', 'mauritania', 
    'mauritius', 'mexico', 'micronesia', 'moldova', 'monaco', 'mongolia', 
    'montenegro', 'morocco', 'mozambique', 'myanmar (formerly burma)', 'namibia', 
    'nauru', 'nepal', 'nicaragua', 'niger', 'nigeria', 'north macedonia', 
    'oman', 'pakistan', 'palau', 'palestine state', 'panama', 'papua new guinea', 
    'paraguay', 'philippines', 'qatar', 'samoa', 'san marino', 
    'sao tome and principe', 'saudi arabia', 'senegal', 'serbia', 'seychelles', 
    'sierra leone', 'singapore', 'sint maarten', 'slovakia', 'slovenia', 
    'solomon islands', 'somalia', 'south sudan', 'sri lanka', 'sudan', 
    'suriname', 'syria', 'taiwan', 'tajikistan', 'tanzania', 'thailand', 
    'timor-leste', 'togo', 'tonga', 'trinidad and tobago', 'tunisia', 'turkey', 
    'turkmenistan', 'tuvalu', 'uganda', 'ukraine', 'uruguay', 
    'uzbekistan', 'vanuatu', 'vatican city (holy see)', 'venezuela', 'vietnam', 
    'yemen', 'zambia', 'zimbabwe', 'united arab emirates', 'new zealand', 
    'south korea', 'north korea' 
}

def get_db_connection():
    return mysql.connector.connect(**DB_CONFIG)

def backup_country_data(cursor):
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    backup_table_name = f'users_country_backup_{timestamp}'
    try:
        cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT id, country FROM users")
        print(f"Successfully created backup table: {backup_table_name}")
        return True
    except mysql.connector.Error as err:
        print(f"Error creating backup table: {err}")
        return False

def get_unique_countries_from_db(cursor):
    cursor.execute("SELECT DISTINCT country FROM users WHERE country IS NOT NULL AND country != ''")
    return [row[0] for row in cursor.fetchall()]

def standardize_name(raw_name):
    if not raw_name or not isinstance(raw_name, str):
        return None
    
    name = raw_name.strip()
    if not name:
        return None
        
    name_lower = name.lower()

    # 1. Direct mapping
    if name_lower in COUNTRY_MAPPING:
        return COUNTRY_MAPPING[name_lower]

    # 2. Handle simple cases like ' The Netherlands ' -> 'Netherlands'
    if name_lower.startswith('the '):
        processed_name_lower = name_lower[4:]
        if processed_name_lower in COUNTRY_MAPPING:
             return COUNTRY_MAPPING[processed_name_lower]

    # 3. Check if it's already a known country (after title casing)
    # Simple title casing for potential valid names not in mapping
    title_cased_name = ' '.join(word.capitalize() for word in name.split())
    if title_cased_name.lower() in KNOWN_COUNTRIES_LOWER:
        return title_cased_name

    # 4. Garbage check
    for pattern in GARBAGE_PATTERNS:
        if pattern.search(name):
            # print(f"Flagged as garbage by pattern {pattern.pattern}: {raw_name}")
            return None # Flag as Null
    
    # Heuristic: if very long and no spaces, likely garbage or specific ID
    if len(name) > 30 and ' ' not in name:
        # print(f"Flagged as garbage (long, no space): {raw_name}")
        return None

    # If it's a short code (e.g. 2-3 letters all caps) not in mapping, could be an unmapped code
    if len(name) <= 3 and name.isupper() and name.isalpha():
        # print(f"Possible unmapped code: {raw_name}")
        return None # Or handle as a special category

    # If not mapped and not clearly garbage, keep original but title-cased for consistency
    # (or decide to NULL them if confidence is low)
    # For now, let's be conservative and NULL if not in KNOWN_COUNTRIES_LOWER
    if title_cased_name.lower() not in KNOWN_COUNTRIES_LOWER:
        # print(f"Unmapped and not in known list: {raw_name} -> NULL")
        return None

    return title_cased_name # Fallback to title-cased if it passed checks

def main():
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        print("Step 1: Backing up country data...")
        if not backup_country_data(cursor):
            print("Backup failed. Aborting operation.")
            return
        conn.commit() # Commit backup creation

        print("\nStep 2: Fetching unique country names from database...")
        raw_countries = get_unique_countries_from_db(cursor)
        print(f"Found {len(raw_countries)} unique raw country entries.")

        print("\nStep 3: Standardizing country names...")
        update_map = {}
        standardized_counts = Counter()
        null_count = 0
        unmapped_but_kept_count = 0

        for raw_name in raw_countries:
            standardized = standardize_name(raw_name)
            # print(f"Raw: '{raw_name}' -> Std: '{standardized}'") 
            if standardized != raw_name: # Only consider actual changes
                if raw_name not in update_map: # Store the first mapping found
                    update_map[raw_name] = standardized
            
            if standardized:
                standardized_counts[standardized] +=1
            else:
                null_count +=1
                if raw_name not in update_map:
                     update_map[raw_name] = None # Ensure even unmapped to NULL are recorded for update
        
        print(f"Processed {len(raw_countries)} entries.")
        print(f"Total entries to be set to NULL: {sum(1 for v in update_map.values() if v is None)}")
        print(f"Total entries to be changed (excluding NULL): {sum(1 for v in update_map.values() if v is not None)}")

        print("\nStep 4: Applying updates to the database...")
        updated_row_count = 0
        for raw_name, new_name in update_map.items():
            try:
                sql = "UPDATE users SET country = %s WHERE country = %s"
                params = (new_name, raw_name)
                cursor.execute(sql, params)
                updated_row_count += cursor.rowcount
            except mysql.connector.Error as err:
                print(f"Error updating '{raw_name}' to '{new_name}': {err}")
        
        conn.commit()
        print(f"Database updates complete. Total rows affected: {updated_row_count}")

        print("\n--- Standardization Summary ---")
        final_counts_cursor = conn.cursor()
        final_counts_cursor.execute("SELECT country, COUNT(*) as count FROM users GROUP BY country ORDER BY count DESC")
        print("Top countries after standardization:")
        for i, (country, count) in enumerate(final_counts_cursor.fetchall()):
            if i < 20:
                print(f"  {country if country else 'NULL'}: {count}")
            elif i == 20:
                print("  ...")
        final_counts_cursor.close()

        print("\nData cleaning process finished.")

    except mysql.connector.Error as err:
        print(f"Database error: {err}")
        if conn: # Rollback in case of error during updates
            conn.rollback()
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    main()

