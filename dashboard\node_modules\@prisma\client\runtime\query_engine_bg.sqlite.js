"use strict";var q=Object.defineProperty;var U=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var C=(n,t)=>{for(var e in t)q(n,e,{get:t[e],enumerable:!0})},$=(n,t,e,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let c of B(t))!N.call(n,c)&&c!==e&&q(n,c,{get:()=>t[c],enumerable:!(o=U(t,c))||o.enumerable});return n};var V=n=>$(q({},"__esModule",{value:!0}),n);var Ft={};C(Ft,{QueryEngine:()=>Y,__wbg_String_88810dfeb4021902:()=>Bn,__wbg_buffer_b7b08af79b0b0974:()=>Cn,__wbg_call_1084a111329e68ce:()=>Zn,__wbg_call_89af060b4e1523f2:()=>gt,__wbg_crypto_58f13aa23ffcb166:()=>Wn,__wbg_done_bfda7aa8f252b39f:()=>rt,__wbg_entries_7a0e06255456ebcd:()=>It,__wbg_getRandomValues_504510b5564925af:()=>Ln,__wbg_getTime_91058879093a1589:()=>fn,__wbg_get_224d16597dbbfd96:()=>ct,__wbg_get_3baa728f9d58d3f6:()=>nt,__wbg_get_94990005bd6ca07c:()=>Un,__wbg_getwithrefkey_5e6d9547403deab8:()=>Dn,__wbg_globalThis_86b222e13bdf32ed:()=>st,__wbg_global_e5a3fe56f8be9485:()=>ft,__wbg_has_4bfbc01db38743f7:()=>cn,__wbg_instanceof_ArrayBuffer_61dfc3198373c902:()=>Tt,__wbg_instanceof_Promise_ae8c7ffdec83f2ae:()=>pn,__wbg_instanceof_Uint8Array_247a91427532499e:()=>ht,__wbg_isArray_8364a5371e9737d8:()=>bt,__wbg_isSafeInteger_7f1ed56200d90674:()=>lt,__wbg_iterator_888179a48810a9fe:()=>xn,__wbg_keys_7840ae453e408eab:()=>gn,__wbg_length_8339fcf5d8ecd12e:()=>xt,__wbg_length_ae22078168b726f5:()=>ln,__wbg_msCrypto_abcb1295e768d1f2:()=>Kn,__wbg_new0_65387337a95cf44d:()=>sn,__wbg_new_525245e2b9901204:()=>vn,__wbg_new_8608a2b51a5f6737:()=>kn,__wbg_new_a220cf903aa02ca2:()=>On,__wbg_new_b85e72ed1bfd57f9:()=>nn,__wbg_new_ea1883e1e5e86686:()=>Vn,__wbg_newnoargs_76313bd6ff35d0f2:()=>at,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9:()=>$n,__wbg_newwithlength_ec548f448387c968:()=>Xn,__wbg_next_de3e9db4440638b2:()=>ot,__wbg_next_f9cb570345655b9a:()=>et,__wbg_node_523d7bd03ef69fba:()=>Hn,__wbg_now_28a6b413aca4a96a:()=>mt,__wbg_now_8ed1a4454e40ecd1:()=>an,__wbg_now_b7a162010a9e75b4:()=>bn,__wbg_parse_52202f117ec9ecfa:()=>un,__wbg_process_5b786e71d465a513:()=>Jn,__wbg_push_37c89022f34c01ca:()=>Mn,__wbg_randomFillSync_a0d98aa11c81fe89:()=>Pn,__wbg_require_2784e593a4674877:()=>Gn,__wbg_resolve_570458cb99d56a43:()=>vt,__wbg_self_3093d5d1f7bcb682:()=>it,__wbg_setTimeout_631fe61f31fa2fad:()=>tn,__wbg_set_49185437f0ab06f8:()=>En,__wbg_set_673dda6c73d19609:()=>qn,__wbg_set_841ac57cff3d672b:()=>Rn,__wbg_set_d1e79e2388520f18:()=>pt,__wbg_set_eacc7d73fefaafdf:()=>dt,__wbg_set_wasm:()=>z,__wbg_stringify_bbf45426c92a6bf5:()=>wt,__wbg_subarray_7c2e3576afe181d1:()=>zn,__wbg_then_876bb3c633745cc6:()=>kt,__wbg_then_95e6edc0f89b73b1:()=>qt,__wbg_valueOf_c759749a331da0c0:()=>tt,__wbg_value_6d39332ab4788d86:()=>_t,__wbg_versions_c2ab80650590b6a2:()=>Qn,__wbg_window_3bcfc4d31bc012f8:()=>ut,__wbindgen_bigint_from_i64:()=>Tn,__wbindgen_bigint_from_u64:()=>Sn,__wbindgen_bigint_get_as_i64:()=>jt,__wbindgen_boolean_get:()=>mn,__wbindgen_cb_drop:()=>Ot,__wbindgen_closure_wrapper6843:()=>Et,__wbindgen_debug_string:()=>At,__wbindgen_error_new:()=>rn,__wbindgen_in:()=>In,__wbindgen_is_bigint:()=>yn,__wbindgen_is_function:()=>Yn,__wbindgen_is_object:()=>wn,__wbindgen_is_string:()=>Fn,__wbindgen_is_undefined:()=>on,__wbindgen_jsval_eq:()=>jn,__wbindgen_jsval_loose_eq:()=>yt,__wbindgen_memory:()=>Nn,__wbindgen_number_get:()=>hn,__wbindgen_number_new:()=>An,__wbindgen_object_clone_ref:()=>_n,__wbindgen_object_drop_ref:()=>dn,__wbindgen_string_get:()=>Z,__wbindgen_string_new:()=>en,__wbindgen_throw:()=>St,debug_panic:()=>K,getBuildTimeInfo:()=>G});module.exports=V(Ft);var S=()=>{};S.prototype=S;let _;function z(n){_=n}const p=new Array(128).fill(void 0);p.push(void 0,null,!0,!1);function r(n){return p[n]}let a=0,j=null;function A(){return(j===null||j.byteLength===0)&&(j=new Uint8Array(_.memory.buffer)),j}const L=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let O=new L("utf-8");const P=typeof O.encodeInto=="function"?function(n,t){return O.encodeInto(n,t)}:function(n,t){const e=O.encode(n);return t.set(e),{read:n.length,written:e.length}};function b(n,t,e){if(e===void 0){const s=O.encode(n),w=t(s.length,1)>>>0;return A().subarray(w,w+s.length).set(s),a=s.length,w}let o=n.length,c=t(o,1)>>>0;const f=A();let u=0;for(;u<o;u++){const s=n.charCodeAt(u);if(s>127)break;f[c+u]=s}if(u!==o){u!==0&&(n=n.slice(u)),c=e(c,o,o=u+n.length*3,1)>>>0;const s=A().subarray(c+u,c+o),w=P(n,s);u+=w.written,c=e(c,o,u,1)>>>0}return a=u,c}function y(n){return n==null}let h=null;function d(){return(h===null||h.buffer.detached===!0||h.buffer.detached===void 0&&h.buffer!==_.memory.buffer)&&(h=new DataView(_.memory.buffer)),h}const W=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let v=new W("utf-8",{ignoreBOM:!0,fatal:!0});v.decode();function T(n,t){return n=n>>>0,v.decode(A().subarray(n,n+t))}let I=p.length;function i(n){I===p.length&&p.push(p.length+1);const t=I;return I=p[t],p[t]=n,t}function J(n){n<132||(p[n]=I,I=n)}function g(n){const t=r(n);return J(n),t}function k(n){const t=typeof n;if(t=="number"||t=="boolean"||n==null)return`${n}`;if(t=="string")return`"${n}"`;if(t=="symbol"){const c=n.description;return c==null?"Symbol":`Symbol(${c})`}if(t=="function"){const c=n.name;return typeof c=="string"&&c.length>0?`Function(${c})`:"Function"}if(Array.isArray(n)){const c=n.length;let f="[";c>0&&(f+=k(n[0]));for(let u=1;u<c;u++)f+=", "+k(n[u]);return f+="]",f}const e=/\[object ([^\]]+)\]/.exec(toString.call(n));let o;if(e.length>1)o=e[1];else return toString.call(n);if(o=="Object")try{return"Object("+JSON.stringify(n)+")"}catch{return"Object"}return n instanceof Error?`${n.name}: ${n.message}
${n.stack}`:o}const E=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>{_.__wbindgen_export_2.get(n.dtor)(n.a,n.b)});function Q(n,t,e,o){const c={a:n,b:t,cnt:1,dtor:e},f=(...u)=>{c.cnt++;const s=c.a;c.a=0;try{return o(s,c.b,...u)}finally{--c.cnt===0?(_.__wbindgen_export_2.get(c.dtor)(s,c.b),E.unregister(c)):c.a=s}};return f.original=c,E.register(f,c,c),f}function H(n,t,e){_._dyn_core__ops__function__FnMut__A____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h9f2fcea3357d311c(n,t,i(e))}function G(){const n=_.getBuildTimeInfo();return g(n)}function K(n){try{const f=_.__wbindgen_add_to_stack_pointer(-16);var t=y(n)?0:b(n,_.__wbindgen_malloc,_.__wbindgen_realloc),e=a;_.debug_panic(f,t,e);var o=d().getInt32(f+4*0,!0),c=d().getInt32(f+4*1,!0);if(c)throw g(o)}finally{_.__wbindgen_add_to_stack_pointer(16)}}function l(n,t){try{return n.apply(this,t)}catch(e){_.__wbindgen_exn_store(i(e))}}function X(n,t,e,o){_.wasm_bindgen__convert__closures__invoke2_mut__h157f32060ce6ea34(n,t,i(e),i(o))}const F=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>_.__wbg_queryengine_free(n>>>0,1));class Y{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,F.unregister(this),t}free(){const t=this.__destroy_into_raw();_.__wbg_queryengine_free(t,0)}constructor(t,e,o){try{const s=_.__wbindgen_add_to_stack_pointer(-16);_.queryengine_new(s,i(t),i(e),i(o));var c=d().getInt32(s+4*0,!0),f=d().getInt32(s+4*1,!0),u=d().getInt32(s+4*2,!0);if(u)throw g(f);return this.__wbg_ptr=c>>>0,F.register(this,this.__wbg_ptr,this),this}finally{_.__wbindgen_add_to_stack_pointer(16)}}connect(t,e){const o=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),c=a,f=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),u=a,s=_.queryengine_connect(this.__wbg_ptr,o,c,f,u);return g(s)}disconnect(t,e){const o=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),c=a,f=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),u=a,s=_.queryengine_disconnect(this.__wbg_ptr,o,c,f,u);return g(s)}query(t,e,o,c){const f=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),u=a,s=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),w=a;var x=y(o)?0:b(o,_.__wbindgen_malloc,_.__wbindgen_realloc),m=a;const R=b(c,_.__wbindgen_malloc,_.__wbindgen_realloc),D=a,M=_.queryengine_query(this.__wbg_ptr,f,u,s,w,x,m,R,D);return g(M)}startTransaction(t,e,o){const c=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),f=a,u=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),s=a,w=b(o,_.__wbindgen_malloc,_.__wbindgen_realloc),x=a,m=_.queryengine_startTransaction(this.__wbg_ptr,c,f,u,s,w,x);return g(m)}commitTransaction(t,e,o){const c=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),f=a,u=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),s=a,w=b(o,_.__wbindgen_malloc,_.__wbindgen_realloc),x=a,m=_.queryengine_commitTransaction(this.__wbg_ptr,c,f,u,s,w,x);return g(m)}rollbackTransaction(t,e,o){const c=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),f=a,u=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),s=a,w=b(o,_.__wbindgen_malloc,_.__wbindgen_realloc),x=a,m=_.queryengine_rollbackTransaction(this.__wbg_ptr,c,f,u,s,w,x);return g(m)}metrics(t){const e=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),o=a,c=_.queryengine_metrics(this.__wbg_ptr,e,o);return g(c)}trace(t){const e=b(t,_.__wbindgen_malloc,_.__wbindgen_realloc),o=a,c=_.queryengine_trace(this.__wbg_ptr,e,o);return g(c)}}function Z(n,t){const e=r(t),o=typeof e=="string"?e:void 0;var c=y(o)?0:b(o,_.__wbindgen_malloc,_.__wbindgen_realloc),f=a;d().setInt32(n+4*1,f,!0),d().setInt32(n+4*0,c,!0)}function nn(n,t){try{var e={a:n,b:t},o=(f,u)=>{const s=e.a;e.a=0;try{return X(s,e.b,f,u)}finally{e.a=s}};const c=new Promise(o);return i(c)}finally{e.a=e.b=0}}function tn(n,t){return setTimeout(r(n),t>>>0)}function en(n,t){const e=T(n,t);return i(e)}function rn(n,t){const e=new Error(T(n,t));return i(e)}function _n(n){const t=r(n);return i(t)}function on(n){return r(n)===void 0}function cn(){return l(function(n,t){return Reflect.has(r(n),r(t))},arguments)}function un(){return l(function(n,t){const e=JSON.parse(T(n,t));return i(e)},arguments)}function sn(){return i(new Date)}function fn(n){return r(n).getTime()}function an(n){return r(n).now()}function bn(){return Date.now()}function gn(n){const t=Object.keys(r(n));return i(t)}function ln(n){return r(n).length}function dn(n){g(n)}function wn(n){const t=r(n);return typeof t=="object"&&t!==null}function pn(n){let t;try{t=r(n)instanceof Promise}catch{t=!1}return t}function xn(){return i(Symbol.iterator)}function mn(n){const t=r(n);return typeof t=="boolean"?t?1:0:2}function yn(n){return typeof r(n)=="bigint"}function hn(n,t){const e=r(t),o=typeof e=="number"?e:void 0;d().setFloat64(n+8*1,y(o)?0:o,!0),d().setInt32(n+4*0,!y(o),!0)}function Tn(n){return i(n)}function In(n,t){return r(n)in r(t)}function Sn(n){const t=BigInt.asUintN(64,n);return i(t)}function jn(n,t){return r(n)===r(t)}function An(n){return i(n)}function On(){const n=new Array;return i(n)}function qn(n,t,e){r(n)[t>>>0]=g(e)}function kn(){return i(new Map)}function vn(){const n=new Object;return i(n)}function En(n,t,e){const o=r(n).set(r(t),r(e));return i(o)}function Fn(n){return typeof r(n)=="string"}function Rn(n,t,e){r(n)[g(t)]=g(e)}function Dn(n,t){const e=r(n)[r(t)];return i(e)}function Mn(n,t){return r(n).push(r(t))}function Un(){return l(function(n,t){const e=r(n)[g(t)];return i(e)},arguments)}function Bn(n,t){const e=String(r(t)),o=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),c=a;d().setInt32(n+4*1,c,!0),d().setInt32(n+4*0,o,!0)}function Nn(){const n=_.memory;return i(n)}function Cn(n){const t=r(n).buffer;return i(t)}function $n(n,t,e){const o=new Uint8Array(r(n),t>>>0,e>>>0);return i(o)}function Vn(n){const t=new Uint8Array(r(n));return i(t)}function zn(n,t,e){const o=r(n).subarray(t>>>0,e>>>0);return i(o)}function Ln(){return l(function(n,t){r(n).getRandomValues(r(t))},arguments)}function Pn(){return l(function(n,t){r(n).randomFillSync(g(t))},arguments)}function Wn(n){const t=r(n).crypto;return i(t)}function Jn(n){const t=r(n).process;return i(t)}function Qn(n){const t=r(n).versions;return i(t)}function Hn(n){const t=r(n).node;return i(t)}function Gn(){return l(function(){const n=module.require;return i(n)},arguments)}function Kn(n){const t=r(n).msCrypto;return i(t)}function Xn(n){const t=new Uint8Array(n>>>0);return i(t)}function Yn(n){return typeof r(n)=="function"}function Zn(){return l(function(n,t){const e=r(n).call(r(t));return i(e)},arguments)}function nt(n,t){const e=r(n)[t>>>0];return i(e)}function tt(n){return r(n).valueOf()}function et(){return l(function(n){const t=r(n).next();return i(t)},arguments)}function rt(n){return r(n).done}function _t(n){const t=r(n).value;return i(t)}function ot(n){const t=r(n).next;return i(t)}function ct(){return l(function(n,t){const e=Reflect.get(r(n),r(t));return i(e)},arguments)}function it(){return l(function(){const n=self.self;return i(n)},arguments)}function ut(){return l(function(){const n=window.window;return i(n)},arguments)}function st(){return l(function(){const n=globalThis.globalThis;return i(n)},arguments)}function ft(){return l(function(){const n=global.global;return i(n)},arguments)}function at(n,t){const e=new S(T(n,t));return i(e)}function bt(n){return Array.isArray(r(n))}function gt(){return l(function(n,t,e){const o=r(n).call(r(t),r(e));return i(o)},arguments)}function lt(n){return Number.isSafeInteger(r(n))}function dt(){return l(function(n,t,e){return Reflect.set(r(n),r(t),r(e))},arguments)}function wt(){return l(function(n){const t=JSON.stringify(r(n));return i(t)},arguments)}function pt(n,t,e){r(n).set(r(t),e>>>0)}function xt(n){return r(n).length}function mt(){return l(function(){return Date.now()},arguments)}function yt(n,t){return r(n)==r(t)}function ht(n){let t;try{t=r(n)instanceof Uint8Array}catch{t=!1}return t}function Tt(n){let t;try{t=r(n)instanceof ArrayBuffer}catch{t=!1}return t}function It(n){const t=Object.entries(r(n));return i(t)}function St(n,t){throw new Error(T(n,t))}function jt(n,t){const e=r(t),o=typeof e=="bigint"?e:void 0;d().setBigInt64(n+8*1,y(o)?BigInt(0):o,!0),d().setInt32(n+4*0,!y(o),!0)}function At(n,t){const e=k(r(t)),o=b(e,_.__wbindgen_malloc,_.__wbindgen_realloc),c=a;d().setInt32(n+4*1,c,!0),d().setInt32(n+4*0,o,!0)}function Ot(n){const t=g(n).original;return t.cnt--==1?(t.a=0,!0):!1}function qt(n,t){const e=r(n).then(r(t));return i(e)}function kt(n,t,e){const o=r(n).then(r(t),r(e));return i(o)}function vt(n){const t=Promise.resolve(r(n));return i(t)}function Et(n,t,e){const o=Q(n,t,532,H);return i(o)}0&&(module.exports={QueryEngine,__wbg_String_88810dfeb4021902,__wbg_buffer_b7b08af79b0b0974,__wbg_call_1084a111329e68ce,__wbg_call_89af060b4e1523f2,__wbg_crypto_58f13aa23ffcb166,__wbg_done_bfda7aa8f252b39f,__wbg_entries_7a0e06255456ebcd,__wbg_getRandomValues_504510b5564925af,__wbg_getTime_91058879093a1589,__wbg_get_224d16597dbbfd96,__wbg_get_3baa728f9d58d3f6,__wbg_get_94990005bd6ca07c,__wbg_getwithrefkey_5e6d9547403deab8,__wbg_globalThis_86b222e13bdf32ed,__wbg_global_e5a3fe56f8be9485,__wbg_has_4bfbc01db38743f7,__wbg_instanceof_ArrayBuffer_61dfc3198373c902,__wbg_instanceof_Promise_ae8c7ffdec83f2ae,__wbg_instanceof_Uint8Array_247a91427532499e,__wbg_isArray_8364a5371e9737d8,__wbg_isSafeInteger_7f1ed56200d90674,__wbg_iterator_888179a48810a9fe,__wbg_keys_7840ae453e408eab,__wbg_length_8339fcf5d8ecd12e,__wbg_length_ae22078168b726f5,__wbg_msCrypto_abcb1295e768d1f2,__wbg_new0_65387337a95cf44d,__wbg_new_525245e2b9901204,__wbg_new_8608a2b51a5f6737,__wbg_new_a220cf903aa02ca2,__wbg_new_b85e72ed1bfd57f9,__wbg_new_ea1883e1e5e86686,__wbg_newnoargs_76313bd6ff35d0f2,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9,__wbg_newwithlength_ec548f448387c968,__wbg_next_de3e9db4440638b2,__wbg_next_f9cb570345655b9a,__wbg_node_523d7bd03ef69fba,__wbg_now_28a6b413aca4a96a,__wbg_now_8ed1a4454e40ecd1,__wbg_now_b7a162010a9e75b4,__wbg_parse_52202f117ec9ecfa,__wbg_process_5b786e71d465a513,__wbg_push_37c89022f34c01ca,__wbg_randomFillSync_a0d98aa11c81fe89,__wbg_require_2784e593a4674877,__wbg_resolve_570458cb99d56a43,__wbg_self_3093d5d1f7bcb682,__wbg_setTimeout_631fe61f31fa2fad,__wbg_set_49185437f0ab06f8,__wbg_set_673dda6c73d19609,__wbg_set_841ac57cff3d672b,__wbg_set_d1e79e2388520f18,__wbg_set_eacc7d73fefaafdf,__wbg_set_wasm,__wbg_stringify_bbf45426c92a6bf5,__wbg_subarray_7c2e3576afe181d1,__wbg_then_876bb3c633745cc6,__wbg_then_95e6edc0f89b73b1,__wbg_valueOf_c759749a331da0c0,__wbg_value_6d39332ab4788d86,__wbg_versions_c2ab80650590b6a2,__wbg_window_3bcfc4d31bc012f8,__wbindgen_bigint_from_i64,__wbindgen_bigint_from_u64,__wbindgen_bigint_get_as_i64,__wbindgen_boolean_get,__wbindgen_cb_drop,__wbindgen_closure_wrapper6843,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_is_bigint,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_eq,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_number_new,__wbindgen_object_clone_ref,__wbindgen_object_drop_ref,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw,debug_panic,getBuildTimeInfo});
